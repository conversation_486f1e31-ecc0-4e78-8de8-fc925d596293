-- Statistical Analysis Advisor Tables (Enhanced)
-- This migration creates tables to support the enhanced Statistical Analysis Advisor feature

-- Table to store user preferences for statistical analysis advisor
CREATE TABLE IF NOT EXISTS statistical_advisor_preferences (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  preference_key VARCHAR(100) NOT NULL,
  preference_value JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, preference_key)
);

-- Enhanced table to store analysis workflows and user interactions
CREATE TABLE IF NOT EXISTS analysis_workflows (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  dataset_id UUID, -- Optional reference to user_datasets if available
  session_id VARCHAR(100), -- To group related workflows in a session
  workflow_type VARCHAR(50) NOT NULL, -- 'tree_navigation', 'wizard', 'direct_recommendation'
  navigation_path JSONB NOT NULL, -- Store the tree navigation path taken
  recommended_methods JSONB NOT NULL, -- Array of recommended methods with details
  context_data JSONB, -- Store dataset context that influenced recommendations
  user_selections JSONB, -- Store user's selections and preferences during workflow
  completion_status VARCHAR(20) DEFAULT 'in_progress', -- 'in_progress', 'completed', 'abandoned'
  user_feedback JSONB, -- Store user feedback (helpful, not helpful, etc.)
  was_implemented BOOLEAN DEFAULT FALSE,
  implementation_details JSONB, -- Store details about which methods were actually used
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced table to store statistical method usage analytics
CREATE TABLE IF NOT EXISTS statistical_method_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  method_id VARCHAR(100) NOT NULL,
  method_name VARCHAR(200) NOT NULL,
  method_category VARCHAR(100) NOT NULL,
  subcategory VARCHAR(100), -- New field for more granular categorization
  datastat_route VARCHAR(200), -- Store the actual DataStatPro route
  access_level VARCHAR(20) DEFAULT 'guest', -- guest, standard, pro, edu
  usage_count INTEGER DEFAULT 1,
  success_rate DECIMAL(5,2), -- Percentage of successful implementations
  avg_user_rating DECIMAL(3,2), -- Average user rating (1-5)
  avg_completion_time INTEGER, -- Average time to complete analysis (in seconds)
  common_contexts JSONB, -- Store common data contexts where this method is used
  last_used TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(method_id)
);

-- Add missing columns to existing tables if they don't exist
DO $$
BEGIN
  -- Add access_level column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'statistical_method_analytics' 
                 AND column_name = 'access_level') THEN
    ALTER TABLE statistical_method_analytics ADD COLUMN access_level VARCHAR(20) DEFAULT 'guest';
  END IF;
  
  -- Add subcategory column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'statistical_method_analytics' 
                 AND column_name = 'subcategory') THEN
    ALTER TABLE statistical_method_analytics ADD COLUMN subcategory VARCHAR(100);
  END IF;
  
  -- Add datastat_route column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'statistical_method_analytics' 
                 AND column_name = 'datastat_route') THEN
    ALTER TABLE statistical_method_analytics ADD COLUMN datastat_route VARCHAR(200);
  END IF;
  
  -- Add success_rate column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'statistical_method_analytics' 
                 AND column_name = 'success_rate') THEN
    ALTER TABLE statistical_method_analytics ADD COLUMN success_rate DECIMAL(5,2);
  END IF;
  
  -- Add avg_user_rating column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'statistical_method_analytics' 
                 AND column_name = 'avg_user_rating') THEN
    ALTER TABLE statistical_method_analytics ADD COLUMN avg_user_rating DECIMAL(3,2);
  END IF;
  
  -- Add avg_completion_time column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'statistical_method_analytics' 
                 AND column_name = 'avg_completion_time') THEN
    ALTER TABLE statistical_method_analytics ADD COLUMN avg_completion_time INTEGER;
  END IF;
  
  -- Add common_contexts column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'statistical_method_analytics' 
                 AND column_name = 'common_contexts') THEN
    ALTER TABLE statistical_method_analytics ADD COLUMN common_contexts JSONB;
  END IF;
END $$;

-- New table to store analysis method hierarchy for tree interface
CREATE TABLE IF NOT EXISTS analysis_method_hierarchy (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  method_id VARCHAR(100) NOT NULL,
  parent_category VARCHAR(100), -- Main category (e.g., 'Descriptive', 'Inferential')
  subcategory VARCHAR(100), -- Subcategory (e.g., 'Central Tendency', 'Hypothesis Testing')
  display_order INTEGER DEFAULT 0, -- Order within category
  is_active BOOLEAN DEFAULT TRUE,
  prerequisites JSONB, -- Data requirements and assumptions
  recommendations_context JSONB, -- When to recommend this method
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(method_id)
);

-- Add missing columns to analysis_method_hierarchy table if they don't exist
DO $$
BEGIN
  -- Add parent_category column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'analysis_method_hierarchy' 
                 AND column_name = 'parent_category') THEN
    ALTER TABLE analysis_method_hierarchy ADD COLUMN parent_category VARCHAR(100);
  END IF;
  
  -- Add subcategory column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'analysis_method_hierarchy' 
                 AND column_name = 'subcategory') THEN
    ALTER TABLE analysis_method_hierarchy ADD COLUMN subcategory VARCHAR(100);
  END IF;
  
  -- Add display_order column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'analysis_method_hierarchy' 
                 AND column_name = 'display_order') THEN
    ALTER TABLE analysis_method_hierarchy ADD COLUMN display_order INTEGER DEFAULT 0;
  END IF;
  
  -- Add is_active column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'analysis_method_hierarchy' 
                 AND column_name = 'is_active') THEN
    ALTER TABLE analysis_method_hierarchy ADD COLUMN is_active BOOLEAN DEFAULT TRUE;
  END IF;
  
  -- Add prerequisites column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'analysis_method_hierarchy' 
                 AND column_name = 'prerequisites') THEN
    ALTER TABLE analysis_method_hierarchy ADD COLUMN prerequisites JSONB;
  END IF;
  
  -- Add recommendations_context column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'analysis_method_hierarchy' 
                 AND column_name = 'recommendations_context') THEN
    ALTER TABLE analysis_method_hierarchy ADD COLUMN recommendations_context JSONB;
  END IF;
END $$;

-- New table to store user bookmarks and saved workflows
CREATE TABLE IF NOT EXISTS advisor_bookmarks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  bookmark_type VARCHAR(50) NOT NULL, -- 'method', 'workflow', 'category'
  reference_id VARCHAR(100) NOT NULL, -- method_id, workflow_id, or category name
  bookmark_data JSONB NOT NULL, -- Store bookmark details
  notes TEXT, -- User notes about the bookmark
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, reference_id, bookmark_type)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_statistical_advisor_preferences_user_id
  ON statistical_advisor_preferences(user_id);

CREATE INDEX IF NOT EXISTS idx_analysis_workflows_user_id
  ON analysis_workflows(user_id);

CREATE INDEX IF NOT EXISTS idx_analysis_workflows_session_id
  ON analysis_workflows(session_id);

CREATE INDEX IF NOT EXISTS idx_analysis_workflows_type
  ON analysis_workflows(workflow_type);

CREATE INDEX IF NOT EXISTS idx_analysis_workflows_status
  ON analysis_workflows(completion_status);

CREATE INDEX IF NOT EXISTS idx_statistical_method_analytics_method_id
  ON statistical_method_analytics(method_id);

CREATE INDEX IF NOT EXISTS idx_statistical_method_analytics_category
  ON statistical_method_analytics(method_category);

CREATE INDEX IF NOT EXISTS idx_statistical_method_analytics_access_level
  ON statistical_method_analytics(access_level);

CREATE INDEX IF NOT EXISTS idx_analysis_method_hierarchy_category
  ON analysis_method_hierarchy(parent_category);

CREATE INDEX IF NOT EXISTS idx_analysis_method_hierarchy_subcategory
  ON analysis_method_hierarchy(subcategory);

CREATE INDEX IF NOT EXISTS idx_analysis_method_hierarchy_order
  ON analysis_method_hierarchy(display_order);

CREATE INDEX IF NOT EXISTS idx_advisor_bookmarks_user_id
  ON advisor_bookmarks(user_id);

CREATE INDEX IF NOT EXISTS idx_advisor_bookmarks_type
  ON advisor_bookmarks(bookmark_type);

-- Enable Row Level Security (RLS)
ALTER TABLE statistical_advisor_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE analysis_workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE statistical_method_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE analysis_method_hierarchy ENABLE ROW LEVEL SECURITY;
ALTER TABLE advisor_bookmarks ENABLE ROW LEVEL SECURITY;

-- RLS Policies for statistical_advisor_preferences
DROP POLICY IF EXISTS "Users can view their own statistical advisor preferences" ON statistical_advisor_preferences;
CREATE POLICY "Users can view their own statistical advisor preferences"
  ON statistical_advisor_preferences FOR SELECT
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own statistical advisor preferences" ON statistical_advisor_preferences;
CREATE POLICY "Users can insert their own statistical advisor preferences"
  ON statistical_advisor_preferences FOR INSERT
  WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own statistical advisor preferences" ON statistical_advisor_preferences;
CREATE POLICY "Users can update their own statistical advisor preferences"
  ON statistical_advisor_preferences FOR UPDATE
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own statistical advisor preferences" ON statistical_advisor_preferences;
CREATE POLICY "Users can delete their own statistical advisor preferences"
  ON statistical_advisor_preferences FOR DELETE
  USING (auth.uid() = user_id);

-- RLS Policies for analysis_workflows
DROP POLICY IF EXISTS "Users can view their own analysis workflows" ON analysis_workflows;
CREATE POLICY "Users can view their own analysis workflows"
  ON analysis_workflows FOR SELECT
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own analysis workflows" ON analysis_workflows;
CREATE POLICY "Users can insert their own analysis workflows"
  ON analysis_workflows FOR INSERT
  WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own analysis workflows" ON analysis_workflows;
CREATE POLICY "Users can update their own analysis workflows"
  ON analysis_workflows FOR UPDATE
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own analysis workflows" ON analysis_workflows;
CREATE POLICY "Users can delete their own analysis workflows"
  ON analysis_workflows FOR DELETE
  USING (auth.uid() = user_id);

-- RLS Policies for statistical_method_analytics (read-only for users, admin can modify)
DROP POLICY IF EXISTS "Users can view statistical method analytics" ON statistical_method_analytics;
CREATE POLICY "Users can view statistical method analytics"
  ON statistical_method_analytics FOR SELECT
  TO authenticated
  USING (true);

DROP POLICY IF EXISTS "Only service role can modify statistical method analytics" ON statistical_method_analytics;
CREATE POLICY "Only service role can modify statistical method analytics"
  ON statistical_method_analytics FOR ALL
  TO service_role
  USING (true);

-- RLS Policies for analysis_method_hierarchy (read-only for users, admin can modify)
DROP POLICY IF EXISTS "Users can view analysis method hierarchy" ON analysis_method_hierarchy;
CREATE POLICY "Users can view analysis method hierarchy"
  ON analysis_method_hierarchy FOR SELECT
  TO authenticated
  USING (true);

DROP POLICY IF EXISTS "Only service role can modify analysis method hierarchy" ON analysis_method_hierarchy;
CREATE POLICY "Only service role can modify analysis method hierarchy"
  ON analysis_method_hierarchy FOR ALL
  TO service_role
  USING (true);

-- RLS Policies for advisor_bookmarks
DROP POLICY IF EXISTS "Users can view their own advisor bookmarks" ON advisor_bookmarks;
CREATE POLICY "Users can view their own advisor bookmarks"
  ON advisor_bookmarks FOR SELECT
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own advisor bookmarks" ON advisor_bookmarks;
CREATE POLICY "Users can insert their own advisor bookmarks"
  ON advisor_bookmarks FOR INSERT
  WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own advisor bookmarks" ON advisor_bookmarks;
CREATE POLICY "Users can update their own advisor bookmarks"
  ON advisor_bookmarks FOR UPDATE
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own advisor bookmarks" ON advisor_bookmarks;
CREATE POLICY "Users can delete their own advisor bookmarks"
  ON advisor_bookmarks FOR DELETE
  USING (auth.uid() = user_id);

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON statistical_advisor_preferences TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON analysis_workflows TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON advisor_bookmarks TO authenticated;
GRANT SELECT ON statistical_method_analytics TO authenticated;
GRANT SELECT ON analysis_method_hierarchy TO authenticated;

-- Grant full permissions to service role for analytics and hierarchy
GRANT ALL ON statistical_method_analytics TO service_role;
GRANT ALL ON analysis_method_hierarchy TO service_role;

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update the updated_at column
DROP TRIGGER IF EXISTS update_statistical_advisor_preferences_updated_at ON statistical_advisor_preferences;
CREATE TRIGGER update_statistical_advisor_preferences_updated_at
  BEFORE UPDATE ON statistical_advisor_preferences
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_analysis_workflows_updated_at ON analysis_workflows;
CREATE TRIGGER update_analysis_workflows_updated_at
  BEFORE UPDATE ON analysis_workflows
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_statistical_method_analytics_updated_at ON statistical_method_analytics;
CREATE TRIGGER update_statistical_method_analytics_updated_at
  BEFORE UPDATE ON statistical_method_analytics
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_analysis_method_hierarchy_updated_at ON analysis_method_hierarchy;
CREATE TRIGGER update_analysis_method_hierarchy_updated_at
  BEFORE UPDATE ON analysis_method_hierarchy
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_advisor_bookmarks_updated_at ON advisor_bookmarks;
CREATE TRIGGER update_advisor_bookmarks_updated_at
  BEFORE UPDATE ON advisor_bookmarks
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert initial data for statistical method analytics with enhanced fields
INSERT INTO statistical_method_analytics (method_id, method_name, method_category, subcategory, datastat_route, access_level) VALUES
('descriptive_single', 'Descriptive Statistics (Single Variable)', 'Descriptive', 'Central Tendency', '/stats/descriptives', 'guest'),
('frequency_table', 'Frequency Table & Proportions', 'Descriptive', 'Distribution Analysis', '/stats/frequencies', 'guest'),
('descriptive_multiple', 'Comprehensive Descriptive Analysis', 'Descriptive', 'Summary Statistics', '/stats/descriptives', 'guest'),
('normality_tests', 'Normality Tests', 'Descriptive', 'Distribution Testing', '/stats/normality', 'guest'),
('crosstabs', 'Cross-Tabulation Analysis', 'Descriptive', 'Categorical Analysis', '/stats/crosstabs', 'guest'),
('one_sample_ttest', 'One-Sample t-test', 'Inferential', 'Hypothesis Testing', '/inferential-stats/one-sample-ttest', 'guest'),
('independent_ttest', 'Independent Samples t-test', 'Inferential', 'Group Comparison', '/inferential-stats/independent-samples-ttest', 'guest'),
('paired_ttest', 'Paired Samples t-test', 'Inferential', 'Paired Comparison', '/inferential-stats/paired-samples-ttest', 'guest'),
('mann_whitney', 'Mann-Whitney U Test', 'Inferential', 'Non-parametric Tests', '/inferential-stats/mann-whitney-u-test', 'standard'),
('wilcoxon', 'Wilcoxon Signed-Rank Test', 'Inferential', 'Non-parametric Tests', '/inferential-stats/wilcoxon-signed-rank-test', 'standard'),
('chi_square', 'Chi-Square Test of Independence', 'Inferential', 'Association Tests', '/inferential-stats/chi-square-test', 'guest'),
('one_way_anova', 'One-Way ANOVA', 'Inferential', 'Multiple Group Comparison', '/inferential-stats/one-way-anova', 'standard'),
('two_way_anova', 'Two-Way ANOVA', 'Inferential', 'Factorial Analysis', '/inferential-stats/two-way-anova', 'pro'),
('repeated_measures_anova', 'Repeated Measures ANOVA', 'Inferential', 'Repeated Measures', '/inferential-stats/repeated-measures-anova', 'pro'),
('kruskal_wallis', 'Kruskal-Wallis Test', 'Inferential', 'Non-parametric Tests', '/inferential-stats/kruskal-wallis-test', 'standard')
ON CONFLICT (method_id) DO NOTHING;

-- Insert initial data for analysis method hierarchy
INSERT INTO analysis_method_hierarchy (method_id, parent_category, subcategory, display_order, prerequisites, recommendations_context) VALUES
('descriptive_single', 'Descriptive Statistics', 'Central Tendency & Variability', 1, '{"data_types": ["numeric"], "min_observations": 1}', '{"when_to_use": "Summarize a single numeric variable", "data_context": "Any numeric data"}'),
('frequency_table', 'Descriptive Statistics', 'Distribution Analysis', 2, '{"data_types": ["categorical", "numeric"], "min_observations": 1}', '{"when_to_use": "Examine distribution of categorical or discrete data", "data_context": "Categorical variables or discrete numeric data"}'),
('normality_tests', 'Descriptive Statistics', 'Distribution Testing', 3, '{"data_types": ["numeric"], "min_observations": 3}', '{"when_to_use": "Test if data follows normal distribution", "data_context": "Before parametric tests"}'),
('crosstabs', 'Descriptive Statistics', 'Categorical Analysis', 4, '{"data_types": ["categorical"], "min_observations": 1, "min_variables": 2}', '{"when_to_use": "Examine relationships between categorical variables", "data_context": "Two or more categorical variables"}'),
('one_sample_ttest', 'Inferential Statistics', 'Single Sample Tests', 1, '{"data_types": ["numeric"], "min_observations": 30, "assumptions": ["normality"]}', '{"when_to_use": "Compare sample mean to known population value", "data_context": "One numeric variable with known population parameter"}'),
('independent_ttest', 'Inferential Statistics', 'Two Sample Tests', 2, '{"data_types": ["numeric"], "min_observations": 30, "assumptions": ["normality", "independence"]}', '{"when_to_use": "Compare means between two independent groups", "data_context": "One numeric variable, one grouping variable with 2 levels"}'),
('paired_ttest', 'Inferential Statistics', 'Paired Tests', 3, '{"data_types": ["numeric"], "min_observations": 30, "assumptions": ["normality"]}', '{"when_to_use": "Compare paired observations or before/after measurements", "data_context": "Two related numeric measurements"}'),
('mann_whitney', 'Inferential Statistics', 'Non-parametric Tests', 4, '{"data_types": ["numeric", "ordinal"], "min_observations": 10}', '{"when_to_use": "Compare two groups when normality assumptions are violated", "data_context": "Non-normal data or ordinal variables"}'),
('wilcoxon', 'Inferential Statistics', 'Non-parametric Tests', 5, '{"data_types": ["numeric", "ordinal"], "min_observations": 10}', '{"when_to_use": "Compare paired observations when normality assumptions are violated", "data_context": "Non-normal paired data"}'),
('chi_square', 'Inferential Statistics', 'Association Tests', 6, '{"data_types": ["categorical"], "min_observations": 5, "min_variables": 2}', '{"when_to_use": "Test association between categorical variables", "data_context": "Two categorical variables"}'),
('one_way_anova', 'Inferential Statistics', 'Multiple Group Tests', 7, '{"data_types": ["numeric"], "min_observations": 30, "assumptions": ["normality", "homogeneity"]}', '{"when_to_use": "Compare means across 3+ groups", "data_context": "One numeric variable, one grouping variable with 3+ levels"}'),
('two_way_anova', 'Inferential Statistics', 'Factorial Analysis', 8, '{"data_types": ["numeric"], "min_observations": 30, "assumptions": ["normality", "homogeneity"]}', '{"when_to_use": "Examine effects of two factors and their interaction", "data_context": "One numeric variable, two grouping variables"}'),
('repeated_measures_anova', 'Inferential Statistics', 'Repeated Measures', 9, '{"data_types": ["numeric"], "min_observations": 30, "assumptions": ["normality", "sphericity"]}', '{"when_to_use": "Compare means across repeated measurements", "data_context": "Multiple measurements on same subjects"}'),
('kruskal_wallis', 'Inferential Statistics', 'Non-parametric Tests', 10, '{"data_types": ["numeric", "ordinal"], "min_observations": 10}', '{"when_to_use": "Compare 3+ groups when normality assumptions are violated", "data_context": "Non-normal data with multiple groups"}')
ON CONFLICT (method_id) DO NOTHING;

-- Comments for documentation
COMMENT ON TABLE statistical_advisor_preferences IS 'Stores user preferences for the Statistical Analysis Advisor feature';
COMMENT ON TABLE analysis_workflows IS 'Stores analysis workflows and user interactions with the enhanced advisor';
COMMENT ON TABLE statistical_method_analytics IS 'Tracks usage analytics for statistical methods with enhanced metadata';
COMMENT ON TABLE analysis_method_hierarchy IS 'Defines the hierarchical structure of analysis methods for tree interface';
COMMENT ON TABLE advisor_bookmarks IS 'Stores user bookmarks for methods, workflows, and categories';

COMMENT ON COLUMN statistical_advisor_preferences.preference_key IS 'Key for the preference (e.g., "interface_mode", "show_assumptions", "default_confidence_level")';
COMMENT ON COLUMN statistical_advisor_preferences.preference_value IS 'JSON value for the preference';

COMMENT ON COLUMN analysis_workflows.navigation_path IS 'JSON array of the tree navigation path taken by the user';
COMMENT ON COLUMN analysis_workflows.recommended_methods IS 'Array of recommended methods with details and reasoning';
COMMENT ON COLUMN analysis_workflows.context_data IS 'Dataset context that influenced the recommendations';
COMMENT ON COLUMN analysis_workflows.user_selections IS 'User selections and preferences during the workflow';

COMMENT ON COLUMN statistical_method_analytics.success_rate IS 'Percentage of users who successfully implemented this method';
COMMENT ON COLUMN statistical_method_analytics.avg_user_rating IS 'Average user rating for this method (1-5 scale)';
COMMENT ON COLUMN statistical_method_analytics.avg_completion_time IS 'Average time to complete analysis in seconds';
COMMENT ON COLUMN statistical_method_analytics.common_contexts IS 'JSON object storing common data contexts where this method is used';

COMMENT ON COLUMN analysis_method_hierarchy.prerequisites IS 'JSON object defining data requirements and statistical assumptions';
COMMENT ON COLUMN analysis_method_hierarchy.recommendations_context IS 'JSON object defining when and why to recommend this method';