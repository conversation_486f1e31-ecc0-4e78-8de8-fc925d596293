# Supabase Environment Variables Setup Guide

## Quick Setup for Email Verification

### Step 1: Access Supabase Dashboard
1. Go to [supabase.com](https://supabase.com)
2. Sign in to your account
3. Select your DataStatPro project

### Step 2: Navigate to Edge Functions Settings
1. In the left sidebar, click **Settings**
2. Click **Edge Functions**
3. Scroll down to the **Environment Variables** section

### Step 3: Add Required Environment Variables

Add these two environment variables:

#### Variable 1: RESEND_API_KEY
- **Name**: `RESEND_API_KEY`
- **Value**: `re_your_actual_resend_api_key_here`
- **Description**: API key from Resend for sending emails

#### Variable 2: FROM_EMAIL
- **Name**: `FROM_EMAIL`
- **Value**: `<EMAIL>` (or `<EMAIL>`)
- **Description**: The sender email address for verification emails

### Step 4: Save Changes
1. Click **Add variable** for each environment variable
2. Click **Save** to apply the changes
3. Wait for the changes to propagate (usually takes a few seconds)

## Important Notes

### For RESEND_API_KEY:
- Get your API key from [resend.com](https://resend.com)
- Go to **API Keys** → **Create API Key**
- Copy the key (starts with `re_`)
- **Never share this key publicly**

### For FROM_EMAIL:
- Use `<EMAIL>` or `<EMAIL>`
- Make sure this domain is verified in your Resend account
- The domain must match your verified domain in Resend

## Verification Steps

1. After setting the variables, go to **Edge Functions** in your Supabase dashboard
2. Look for the `send-guest-verification-email` function
3. If it's not deployed yet, follow the deployment guide
4. Test the email verification in your DataStatPro app

## Troubleshooting

### Variables Not Working?
- Make sure there are no extra spaces in the variable names or values
- Verify the RESEND_API_KEY is correct and active
- Ensure the FROM_EMAIL domain is verified in Resend
- Try redeploying the Edge Function after setting variables

### Still Not Receiving Emails?
- Check your spam/junk folder
- Verify domain verification is complete in Resend
- Check the Edge Function logs in Supabase for errors
- Make sure the function is deployed and active

## Visual Guide

```
Supabase Dashboard
├── Settings
│   ├── General
│   ├── Database
│   ├── API
│   ├── Authentication
│   ├── Storage
│   └── Edge Functions ← Click here
│       └── Environment Variables ← Scroll to this section
│           ├── Add RESEND_API_KEY
│           └── Add FROM_EMAIL
```

## Next Steps

After setting up the environment variables:
1. Deploy the Edge Function (see EDGE_FUNCTION_DEPLOYMENT_GUIDE.md)
2. Test the email verification feature
3. Monitor the function logs for any issues