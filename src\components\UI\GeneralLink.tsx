import React from 'react';
import { <PERSON>, Box, Tooltip, Typography } from '@mui/material';
import { Launch as LaunchIcon } from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';

interface GeneralLinkProps {
  url: string;
  variant?: 'inline' | 'card';
  showPreview?: boolean;
}

const GeneralLink: React.FC<GeneralLinkProps> = ({
  url,
  variant = 'inline',
  showPreview = true
}) => {
  const theme = useTheme();

  // Extract domain name for display
  const getDomainName = (url: string): string => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch {
      return url;
    }
  };

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  if (variant === 'card') {
    return (
      <Box
        sx={{
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 1,
          p: 2,
          backgroundColor: theme.palette.background.paper,
          cursor: 'pointer',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            backgroundColor: theme.palette.action.hover,
            borderColor: theme.palette.primary.main
          }
        }}
        onClick={handleClick}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <LaunchIcon 
            sx={{ 
              fontSize: 16, 
              color: theme.palette.primary.main 
            }} 
          />
          <Typography 
            variant="body2" 
            sx={{ 
              color: theme.palette.primary.main,
              textDecoration: 'none',
              fontWeight: 500
            }}
          >
            {getDomainName(url)}
          </Typography>
        </Box>
        <Typography 
          variant="caption" 
          sx={{ 
            color: theme.palette.text.secondary,
            mt: 0.5,
            display: 'block',
            wordBreak: 'break-all'
          }}
        >
          {url}
        </Typography>
      </Box>
    );
  }

  // Inline variant
  const linkContent = (
    <Link
      component="button"
      onClick={handleClick}
      sx={{
        color: theme.palette.primary.main,
        textDecoration: 'none',
        display: 'inline-flex',
        alignItems: 'center',
        gap: 0.5,
        fontWeight: 500,
        fontSize: 'inherit',
        '&:hover': {
          textDecoration: 'underline',
          color: theme.palette.primary.dark
        },
        cursor: 'pointer',
        border: 'none',
        background: 'none',
        padding: 0,
        font: 'inherit'
      }}
    >
      {getDomainName(url)}
      <LaunchIcon fontSize="small" sx={{ ml: 0.5, opacity: 0.7 }} />
    </Link>
  );

  if (showPreview) {
    return (
      <Tooltip
        title={
          <Box sx={{ p: 1.5 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <LaunchIcon fontSize="small" color="primary" />
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                External Link
              </Typography>
            </Box>
            <Typography 
              variant="caption" 
              sx={{ 
                display: 'block', 
                mt: 0.5,
                wordBreak: 'break-all',
                maxWidth: 300,
                color: 'text.secondary'
              }}
            >
              {url}
            </Typography>
            <Typography 
              variant="caption" 
              sx={{ 
                display: 'block', 
                mt: 0.5,
                color: 'text.secondary'
              }}
            >
              Click to open in new tab
            </Typography>
          </Box>
        }
        placement="top"
        arrow
        enterDelay={300}
        leaveDelay={200}
      >
        <span>{linkContent}</span>
      </Tooltip>
    );
  }

  return linkContent;
};

export default GeneralLink;