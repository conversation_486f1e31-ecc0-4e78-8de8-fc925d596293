import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert,
  Divider,
  useTheme,
  useMediaQuery,
  Fade,
  Slide,
  alpha,
  Menu,
  MenuItem,
  AppBar,
  Toolbar,
  Avatar,
  IconButton,
  Paper,
  Stack
} from '@mui/material';
import {
  YouTube as YouTubeIcon,
  VideoCall as VideoIcon,
  Share as ShareIcon,
  LinkedIn as LinkedInIcon,
  Article as BlogIcon,
  Star as ReviewIcon,
  People as ReferralIcon,
  School as EducationIcon,
  Forum as ForumIcon,
  Email as EmailIcon,
  CheckCircle as CheckIcon,
  ArrowBack as ArrowBackIcon,
  Home as HomeIcon,
  Launch as LaunchIcon,
  ContentCopy as CopyIcon,
  Twitter as TwitterIcon,
  Facebook as FacebookIcon,
  WhatsApp as WhatsAppIcon,
  KeyboardArrowDown as ArrowDownIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface PromotionalPackage {
  id: string;
  title: string;
  reward: string;
  duration: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  actionText: string;
  actionUrl?: string;
  proofRequirements: string[];
  highlighted?: boolean;
  badge?: string;
}

interface AdditionalIdea {
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  suggestedReward: string;
}

const PromotionsPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();
  const [visible, setVisible] = useState(false);
  const [copiedText, setCopiedText] = useState(false);
  const [shareMenuAnchor, setShareMenuAnchor] = useState<null | HTMLElement>(null);

  useEffect(() => {
    const timer = setTimeout(() => setVisible(true), 300);
    return () => clearTimeout(timer);
  }, []);

  const promotionalPackages: PromotionalPackage[] = [
    {
      id: 'youtube-subscribe',
      title: 'YouTube Subscription',
      reward: '1 Month Pro Free',
      duration: 'One-time reward',
      description: 'Subscribe to our YouTube channel for statistical tutorials, feature demos, and research tips. Quick and easy way to support our educational content!',
      icon: <YouTubeIcon sx={{ fontSize: 32 }} />,
      color: '#FF0000',
      actionText: 'Subscribe Now',
      actionUrl: 'https://www.youtube.com/@DataStatProLive',
      proofRequirements: [
        'Screenshot of your subscription confirmation page',
        'Include your YouTube username in the verification email',
        'Must remain subscribed during verification process (usually 1-2 business days)'
      ],
      highlighted: false
    },
    {
      id: 'youtube-video',
      title: 'YouTube Video Creation',
      reward: '6 Months Pro Free',
      duration: 'One-time reward',
      description: 'Create educational content showing how you use DataStatPro for research, teaching, or analysis. Examples: tutorial walkthrough, feature demonstration, research case study, or comparison with other tools.',
      icon: <VideoIcon sx={{ fontSize: 32 }} />,
      color: '#FF6B35',
      actionText: 'Create Video',
      proofRequirements: [
        'Direct link to your published YouTube video',
        'Video must be at least 2 minutes long (educational content)',
        'Must demonstrate actual DataStatPro features or workflow',
        'Video should remain public for at least 30 days after verification',
        'Include "DataStatPro" in video title or description'
      ],
      highlighted: true,
      badge: 'Best Value'
    },
    {
      id: 'social-media',
      title: 'Social Media Post',
      reward: '1 Month Pro Free',
      duration: 'One-time per platform',
      description: 'Share your experience with DataStatPro on social media. Tell your network how it helps with research, teaching, or data analysis. Authentic posts about your actual usage work best!',
      icon: <ShareIcon sx={{ fontSize: 32 }} />,
      color: '#1DA1F2',
      actionText: 'Share Now',
      proofRequirements: [
        'Direct link to your public post (Facebook, Twitter, Instagram, etc.)',
        'Post must mention @DataStatPro or include #DataStatPro hashtag',
        'Include genuine commentary about your experience with the platform',
        'Post must remain public for at least 7 days after verification',
        'One reward per social media platform (can claim on multiple platforms)'
      ],
      highlighted: false
    }
  ];

  const additionalIdeas: AdditionalIdea[] = [
    {
      title: 'LinkedIn Professional Posts',
      description: 'Share how DataStatPro enhances your professional work - research publications, teaching methods, or data analysis workflows. Perfect for academics, researchers, and data professionals.',
      icon: <LinkedInIcon sx={{ fontSize: 24 }} />,
      color: '#0077B5',
      suggestedReward: '1-2 months Pro'
    },
    {
      title: 'Blog Post Creation',
      description: 'Write an in-depth article about your DataStatPro experience - methodology tutorials, research case studies, feature comparisons, or educational guides. Great for personal blogs, Medium, or institutional websites.',
      icon: <BlogIcon sx={{ fontSize: 24 }} />,
      color: '#FF6B6B',
      suggestedReward: '3-6 months Pro'
    },
    {
      title: 'App Store Reviews',
      description: 'Share your honest experience on review platforms like Google Play, App Store, or software review sites. Help other researchers and students discover DataStatPro by describing specific features you find valuable.',
      icon: <ReviewIcon sx={{ fontSize: 24 }} />,
      color: '#FFD93D',
      suggestedReward: '1 month Pro'
    },
    {
      title: 'Referral Programs',
      description: 'Introduce DataStatPro to your academic colleagues, research team members, or students. Especially valuable for department-wide adoption or research group collaborations.',
      icon: <ReferralIcon sx={{ fontSize: 24 }} />,
      color: '#6BCF7F',
      suggestedReward: '1 month per referral'
    },
    {
      title: 'Educational Content',
      description: 'Develop comprehensive learning materials - step-by-step tutorials, statistical method guides, classroom exercises, or research methodology documentation. Perfect for educators and experienced researchers.',
      icon: <EducationIcon sx={{ fontSize: 24 }} />,
      color: '#9C27B0',
      suggestedReward: '3-12 months Pro'
    },
    {
      title: 'Community Participation',
      description: 'Join statistical software discussions on Reddit, Stack Overflow, academic forums, or research communities. Share your DataStatPro solutions to help others with their statistical challenges.',
      icon: <ForumIcon sx={{ fontSize: 24 }} />,
      color: '#FF9800',
      suggestedReward: '1-3 months Pro'
    }
  ];

  const handleCopyText = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopiedText(true);
    setTimeout(() => setCopiedText(false), 2000);
  };

  const handleShareMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setShareMenuAnchor(event.currentTarget);
  };

  const handleShareMenuClose = () => {
    setShareMenuAnchor(null);
  };

  // Crafted messages for different social media platforms
  const shareMessages = {
    twitter: "DataStatPro - Free Statistical Software for Research & Education - Powerful statistical analysis tools for researchers, educators, and students. Free alternative to premium statistical software with AI-powered analysis assistant. http://datastatpro.com/app/home #DataStatPro via @datastatpro #DataAnalysis",
    facebook: "DataStatPro - Free Statistical Software for Research & Education - Powerful statistical analysis tools for researchers, educators, and students. Free alternative to premium statistical software with AI-powered analysis assistant. http://datastatpro.com/app/home #DataStatPro via @datastatpro #DataAnalysis",
    linkedin: "DataStatPro - Free Statistical Software for Research & Education - Powerful statistical analysis tools for researchers, educators, and students. Free alternative to premium statistical software with AI-powered analysis assistant. http://datastatpro.com/app/home #DataStatPro via @datastatpro #DataAnalysis",
    whatsapp: "DataStatPro - Free Statistical Software for Research & Education - Powerful statistical analysis tools for researchers, educators, and students. Free alternative to premium statistical software with AI-powered analysis assistant. http://datastatpro.com/app/home #DataStatPro via @datastatpro #DataAnalysis"
  };

  const handleSocialShare = (platform: string) => {
    const baseUrl = window.location.origin;
    const promotionsUrl = `${baseUrl}/promotions`;
    
    let shareUrl = '';
    
    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareMessages.twitter)}&url=${encodeURIComponent(promotionsUrl)}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(promotionsUrl)}&quote=${encodeURIComponent(shareMessages.facebook)}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(promotionsUrl)}&summary=${encodeURIComponent(shareMessages.linkedin)}`;
        break;
      case 'whatsapp':
        shareUrl = `https://wa.me/?text=${encodeURIComponent(shareMessages.whatsapp + ' ' + promotionsUrl)}`;
        break;
      default:
        return;
    }
    
    window.open(shareUrl, '_blank', 'width=600,height=400');
    handleShareMenuClose();
  };

  const handleEmailSubmission = () => {
    const subject = 'YouTube Video Submission - DataStatPro Promotional Program';
    const body = `Dear DataStatPro Support Team,

I hope this email finds you well. I am writing to submit my YouTube video for the DataStatPro promotional program to claim my 6 months Pro subscription reward.

📹 Video Details:
YouTube Video URL: [Please paste your video URL here]
Video Title: [Your video title]
Publication Date: [Date when you published the video]

✅ Proof Requirements Checklist:
☐ Link to published YouTube video (included above)
☐ Video is at least 2 minutes long
☐ Video demonstrates DataStatPro features
☐ Video remains public for verification

📝 Additional Information:
[Please add any additional details about your video content, which DataStatPro features you showcased, or any other relevant information]

I confirm that my video meets all the requirements listed in the promotional program and I am submitting this from my registered email address.

Thank you for your time and consideration. I look forward to hearing from you soon.

Best regards,
[Your Name]
[Your Registered Email Address]`;
    
    const mailtoUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoUrl;
  };

  const handleGeneralEmailSubmission = () => {
    const subject = 'Promotional Claim Submission - DataStatPro Rewards Program';
    const body = `Dear DataStatPro Support Team,

I hope this email finds you well. I am writing to submit my promotional activity completion for the DataStatPro rewards program.

🎯 Promotional Package Details:
Selected Promotion: [Please specify which promotion you completed]
☐ YouTube Subscription (1 Month Pro Free)
☐ YouTube Video Creation (6 Months Pro Free)
☐ Social Media Post (1 Month Pro Free)

📋 Account Information:
Registered Email: [Your DataStatPro account email]
Account Username: [Your username if applicable]
Date of Activity Completion: [Date when you completed the promotional activity]

📄 Proof Documentation:
[Please attach or describe your proof based on the selected promotion]

For YouTube Subscription:
☐ Screenshot of subscription confirmation
☐ YouTube username included in email
☐ Subscribed when verification was possible

For YouTube Video Creation:
☐ Link to published YouTube video
☐ Video is at least 2 minutes long
☐ Video demonstrates DataStatPro features
☐ Video remains public for verification

For Social Media Post:
☐ Link to public post
☐ Post mentions @DataStatPro or #DataStatPro
☐ Includes positive commentary about the app
☐ Post remains public for verification

📝 Additional Comments:
[Any additional information or questions]

Thank you for your time and consideration. I look forward to hearing from you soon.

Best regards,
[Your Name]`;

    const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(mailtoLink, '_self');
  };

  const notificationText = `🎁 Earn FREE DataStatPro Pro Months by Sharing Us Online!

Help spread the word and enjoy free Pro time:
1️⃣ Subscribe to our YouTube channel → Get 1 Month Pro
   👉 https://www.youtube.com/@DataStatProLive
2️⃣ Create a YouTube video about any DataStatPro feature → Get 6 Months Pro
3️⃣ Post about DataStatPro on Facebook, Twitter, or Instagram → Get 1 Month Pro

✅ How to claim: Email proof (screenshot or link) from your registered <NAME_EMAIL>.
- Proof examples: Subscription screenshot, video link, or public post link.
- Rewards apply once per activity.

📌 Full details & more ways to earn: https://datastatpro.com/promotions`;

  return (
    <Box sx={{ bgcolor: 'background.default', minHeight: '100vh' }}>
      {/* Navigation Header */}
      <AppBar
        position="fixed"
        sx={{
          zIndex: theme.zIndex.drawer + 1,
          boxShadow: '0 2px 10px rgba(0,0,0,0.08)',
          background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
        }}
        elevation={0}
      >
        <Toolbar sx={{
          minHeight: { xs: 56, sm: 64 },
          px: { xs: 1, sm: 2 }
        }}>
          <IconButton
            color="inherit"
            aria-label="go back"
            edge="start"
            onClick={() => navigate(-1)}
            sx={{ mr: 1 }}
            size={isMobile ? "small" : "medium"}
          >
            <ArrowBackIcon />
          </IconButton>

          {/* Logo and Title */}
          <Box
            onClick={() => navigate('/')}
            sx={{
              display: 'flex',
              alignItems: 'center',
              mr: 1,
              cursor: 'pointer',
              textDecoration: 'none',
              color: 'inherit'
            }}
          >
            <Avatar
              src="/logo.png"
              alt="DataStatPro"
              sx={{
                width: { xs: 28, sm: 32 },
                height: { xs: 28, sm: 32 },
                mr: 1.5
              }}
            />
            <Typography
              variant={isMobile ? "subtitle1" : "h6"}
              noWrap
              component="div"
              sx={{
                fontWeight: 'bold',
                letterSpacing: '0.5px'
              }}
            >
              DataStatPro
            </Typography>
          </Box>

          <Box sx={{ flexGrow: 1 }} />

          {/* Home Button */}
          <Button
            color="inherit"
            startIcon={<HomeIcon />}
            onClick={() => navigate('/')}
            sx={{
              ml: 1,
              display: { xs: 'none', sm: 'flex' }
            }}
          >
            Home
          </Button>

          {/* App Button */}
          <Button
            color="inherit"
            onClick={() => navigate('/app/dashboard')}
            sx={{
              ml: 1,
              bgcolor: 'rgba(255, 255, 255, 0.1)',
              '&:hover': {
                bgcolor: 'rgba(255, 255, 255, 0.2)',
              }
            }}
          >
            Launch App
          </Button>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Box sx={{ pt: { xs: 7, sm: 8 }, pb: { xs: 4, md: 8 } }}>
        <Container maxWidth="lg">
          {/* Header Section */}
          <Fade in={visible} timeout={800}>
            <Box textAlign="center" mb={6}>
              <Typography
                variant="h2"
                component="h1"
                fontWeight="bold"
                sx={{
                  mb: 2,
                  fontSize: { xs: '2.5rem', md: '3.5rem' },
                  background: 'linear-gradient(135deg, #FF6B35 0%, #F7931E 50%, #FFD93D 100%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                🎁 Earn Free Pro Time
              </Typography>
              
              {/* Promotional Slogan */}
              <Typography
                variant="h4"
                component="h2"
                fontWeight="bold"
                sx={{
                  mb: 3,
                  fontSize: { xs: '1.8rem', md: '2.2rem' },
                  color: 'primary.main',
                  textShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}
              >
                Small Tasks, Big Rewards
              </Typography>
              
              <Typography
                variant="h5"
                color="text.secondary"
                sx={{ mb: 4, maxWidth: '700px', mx: 'auto', lineHeight: 1.6 }}
              >
                Help us grow and get rewarded with free DataStatPro Pro subscriptions!
              </Typography>
              
              {/* What is DataStatPro Section */}
              <Paper
                elevation={2}
                sx={{
                  maxWidth: '900px',
                  mx: 'auto',
                  mb: 4,
                  p: 3,
                  borderRadius: 3,
                  bgcolor: 'background.paper',
                  border: '1px solid',
                  borderColor: 'divider'
                }}
              >
                <Typography variant="h6" fontWeight="bold" gutterBottom color="primary.main">
                  📊 What is DataStatPro?
                </Typography>
                <Typography variant="body1" sx={{ mb: 2, lineHeight: 1.6 }}>
                  DataStatPro is a comprehensive, free statistical analysis platform designed specifically for researchers, educators, and students. 
                  Our mission is to democratize access to powerful statistical tools that were previously only available in expensive premium software.
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  ✨ Features include: Advanced statistical tests, AI-powered analysis assistant, interactive data visualization, 
                  research-grade reporting, and educational resources - all accessible through your web browser.
                </Typography>
              </Paper>
              
              {/* Why Promotions Section */}
              <Paper
                elevation={2}
                sx={{
                  maxWidth: '900px',
                  mx: 'auto',
                  mb: 4,
                  p: 3,
                  borderRadius: 3,
                  bgcolor: 'primary.main',
                  color: 'white'
                }}
              >
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  🤝 Why We Offer These Rewards
                </Typography>
                <Typography variant="body1" sx={{ mb: 2, lineHeight: 1.6 }}>
                  As a mission-driven platform, we believe in community growth over traditional advertising. 
                  When you share DataStatPro, you're helping fellow researchers and students discover free, powerful statistical tools.
                </Typography>
                <Typography variant="body2" sx={{ lineHeight: 1.6, opacity: 0.9 }}>
                  Your authentic recommendations are more valuable than any advertisement - and we want to reward you for helping build our educational community!
                </Typography>
              </Paper>
              
              {/* Promotional Alert */}
              <Alert
                severity="success"
                sx={{
                  maxWidth: '900px',
                  mx: 'auto',
                  mb: 4,
                  borderRadius: 2,
                  '& .MuiAlert-message': {
                    fontSize: '1rem'
                  }
                }}
              >
                <Typography variant="body1" fontWeight="medium">
                  🚀 Limited Time: Share DataStatPro and Earn Up to 6 Months Pro Free!
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  Join our community promotion program and get rewarded for helping DataStatPro reach more researchers and students.
                </Typography>
              </Alert>
            </Box>
          </Fade>

          {/* Promotional Packages */}
          <Typography variant="h4" fontWeight="bold" textAlign="center" gutterBottom sx={{ mb: 4 }}>
            Choose Your Promotion Method
          </Typography>
          
          <Grid container spacing={4} justifyContent="center" sx={{ mb: 8 }}>
            {promotionalPackages.map((pkg, index) => (
              <Grid item xs={12} sm={6} lg={4} key={pkg.id}>
                <Slide direction="up" in={visible} timeout={800 + index * 200}>
                  <Card
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      position: 'relative',
                      borderRadius: 3,
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      ...(pkg.highlighted && {
                        border: `2px solid ${pkg.color}`,
                        transform: 'scale(1.02)',
                        boxShadow: `0 12px 40px ${alpha(pkg.color, 0.2)}`,
                      }),
                      '&:hover': {
                        transform: pkg.highlighted ? 'scale(1.02)' : 'translateY(-8px)',
                        boxShadow: `0 16px 50px ${alpha(pkg.color, 0.15)}`,
                      }
                    }}
                  >
                    {/* Badge */}
                    {pkg.badge && (
                      <Chip
                        label={pkg.badge}
                        sx={{
                          position: 'absolute',
                          top: 16,
                          right: 16,
                          bgcolor: pkg.color,
                          color: 'white',
                          fontWeight: 'bold',
                          fontSize: '0.75rem'
                        }}
                      />
                    )}

                    <CardContent sx={{ flexGrow: 1, p: 3 }}>
                      {/* Header */}
                      <Box textAlign="center" mb={3}>
                        <Box
                          sx={{
                            width: 64,
                            height: 64,
                            borderRadius: '50%',
                            bgcolor: alpha(pkg.color, 0.1),
                            color: pkg.color,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            mx: 'auto',
                            mb: 2
                          }}
                        >
                          {pkg.icon}
                        </Box>
                        <Typography variant="h5" fontWeight="bold" gutterBottom>
                          {pkg.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {pkg.description}
                        </Typography>
                        <Box>
                          <Typography
                            variant="h4"
                            component="span"
                            fontWeight="bold"
                            color={pkg.color}
                          >
                            {pkg.reward}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" component="span" sx={{ display: 'block', mt: 0.5 }}>
                            {pkg.duration}
                          </Typography>
                        </Box>
                      </Box>

                      <Divider sx={{ mb: 2 }} />

                      {/* Proof Requirements */}
                      <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                        Proof Requirements:
                      </Typography>
                      <List dense sx={{ p: 0 }}>
                        {pkg.proofRequirements.map((requirement, reqIndex) => (
                          <ListItem key={reqIndex} sx={{ px: 0, py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 32 }}>
                              <CheckIcon sx={{ color: 'success.main', fontSize: 16 }} />
                            </ListItemIcon>
                            <ListItemText
                              primary={requirement}
                              primaryTypographyProps={{
                                variant: 'body2',
                                fontSize: '0.85rem'
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>

                    <CardActions sx={{ p: 3, pt: 0 }}>
                      {pkg.id === 'social-media' ? (
                        <>
                          <Button
                            variant="contained"
                            fullWidth
                            size="large"
                            onClick={handleShareMenuOpen}
                            endIcon={<ArrowDownIcon />}
                            sx={{
                              py: 1.5,
                              fontWeight: 'bold',
                              borderRadius: 2,
                              bgcolor: pkg.color,
                              '&:hover': {
                                bgcolor: alpha(pkg.color, 0.8),
                              }
                            }}
                          >
                            {pkg.actionText}
                          </Button>
                          <Menu
                            anchorEl={shareMenuAnchor}
                            open={Boolean(shareMenuAnchor)}
                            onClose={handleShareMenuClose}
                            PaperProps={{
                              sx: {
                                mt: 1,
                                minWidth: 200,
                                borderRadius: 2,
                                boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
                              }
                            }}
                          >
                            <MenuItem onClick={() => handleSocialShare('twitter')}>
                              <ListItemIcon>
                                <TwitterIcon sx={{ color: '#1DA1F2' }} />
                              </ListItemIcon>
                              <ListItemText>Share on Twitter</ListItemText>
                            </MenuItem>
                            <MenuItem onClick={() => handleSocialShare('facebook')}>
                              <ListItemIcon>
                                <FacebookIcon sx={{ color: '#1877F2' }} />
                              </ListItemIcon>
                              <ListItemText>Share on Facebook</ListItemText>
                            </MenuItem>
                            <MenuItem onClick={() => handleSocialShare('linkedin')}>
                              <ListItemIcon>
                                <LinkedInIcon sx={{ color: '#0A66C2' }} />
                              </ListItemIcon>
                              <ListItemText>Share on LinkedIn</ListItemText>
                            </MenuItem>
                            <MenuItem onClick={() => handleSocialShare('whatsapp')}>
                              <ListItemIcon>
                                <WhatsAppIcon sx={{ color: '#25D366' }} />
                              </ListItemIcon>
                              <ListItemText>Share on WhatsApp</ListItemText>
                            </MenuItem>
                            <Divider />
                            <MenuItem onClick={() => handleCopyText(shareMessages.twitter)}>
                              <ListItemIcon>
                                <CopyIcon />
                              </ListItemIcon>
                              <ListItemText>Copy Text</ListItemText>
                            </MenuItem>
                          </Menu>
                        </>
                      ) : pkg.id === 'youtube-video' ? (
                        <Button
                          variant="contained"
                          fullWidth
                          size="large"
                          onClick={handleEmailSubmission}
                          startIcon={<EmailIcon />}
                          sx={{
                            py: 1.5,
                            fontWeight: 'bold',
                            borderRadius: 2,
                            bgcolor: pkg.color,
                            '&:hover': {
                              bgcolor: alpha(pkg.color, 0.8),
                            }
                          }}
                        >
                          Submit via Email
                        </Button>
                      ) : (
                        <Button
                          variant="contained"
                          fullWidth
                          size="large"
                          onClick={() => pkg.actionUrl && window.open(pkg.actionUrl, '_blank')}
                          startIcon={<LaunchIcon />}
                          sx={{
                            py: 1.5,
                            fontWeight: 'bold',
                            borderRadius: 2,
                            bgcolor: pkg.color,
                            '&:hover': {
                              bgcolor: alpha(pkg.color, 0.8),
                            }
                          }}
                        >
                          {pkg.actionText}
                        </Button>
                      )}
                    </CardActions>
                  </Card>
                </Slide>
              </Grid>
            ))}
          </Grid>

          {/* FAQ Section */}
          <Fade in={visible} timeout={900}>
            <Paper
              elevation={3}
              sx={{
                p: 4,
                mb: 6,
                borderRadius: 3,
                bgcolor: 'background.paper'
              }}
            >
              <Typography variant="h4" fontWeight="bold" textAlign="center" gutterBottom sx={{ color: 'primary.main' }}>
                ❓ Frequently Asked Questions
              </Typography>
              
              <Grid container spacing={3} sx={{ mt: 2 }}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                    How long does verification take?
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 3, color: 'text.secondary' }}>
                    Most verifications are processed within 1-2 business days. Complex content (like videos) may take up to 3-5 business days for thorough review.
                  </Typography>
                  
                  <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                    Can I combine multiple promotions?
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 3, color: 'text.secondary' }}>
                    Yes! You can earn rewards from different promotion types. For example, subscribe to YouTube (1 month) + create a video (6 months) = 7 months total Pro time.
                  </Typography>
                  
                  <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                    What if my content doesn't meet requirements?
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    We'll email you with specific feedback and give you a chance to revise. Our goal is to help you succeed, not reject submissions.
                  </Typography>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                    Do I need to be a current Pro subscriber?
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 3, color: 'text.secondary' }}>
                    No! These promotions are open to all users. Free users get Pro time added to their account, existing Pro users get their subscription extended.
                  </Typography>
                  
                  <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                    What makes good promotional content?
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 3, color: 'text.secondary' }}>
                    Authentic experiences work best! Show how you actually use DataStatPro in your research, teaching, or analysis. Specific examples and genuine enthusiasm resonate more than generic praise.
                  </Typography>
                  
                  <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                    Are there any content restrictions?
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    Content should be appropriate, honest, and educational. We reserve the right to decline promotional content that doesn't align with our academic and professional community values.
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
          </Fade>

          {/* Verification Process */}
          <Fade in={visible} timeout={1000}>
            <Paper
              elevation={3}
              sx={{
                p: 4,
                mb: 6,
                borderRadius: 3,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white'
              }}
            >
              <Typography variant="h4" fontWeight="bold" textAlign="center" gutterBottom>
                How to Claim Your Reward
              </Typography>
              <Grid container spacing={3} sx={{ mt: 2 }}>
                <Grid item xs={12} md={4} textAlign="center">
                  <Box
                    sx={{
                      width: 60,
                      height: 60,
                      borderRadius: '50%',
                      bgcolor: 'rgba(255, 255, 255, 0.2)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mx: 'auto',
                      mb: 2
                    }}
                  >
                    <Typography variant="h4" fontWeight="bold">1</Typography>
                  </Box>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Complete Activity
                  </Typography>
                  <Typography variant="body2">
                    Choose and complete one of the promotional activities above
                  </Typography>
                </Grid>
                <Grid item xs={12} md={4} textAlign="center">
                  <Box
                    sx={{
                      width: 60,
                      height: 60,
                      borderRadius: '50%',
                      bgcolor: 'rgba(255, 255, 255, 0.2)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mx: 'auto',
                      mb: 2
                    }}
                  >
                    <Typography variant="h4" fontWeight="bold">2</Typography>
                  </Box>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Gather Proof
                  </Typography>
                  <Typography variant="body2">
                    Take screenshots or collect links as specified in the requirements
                  </Typography>
                </Grid>
                <Grid item xs={12} md={4} textAlign="center">
                  <Box
                    sx={{
                      width: 60,
                      height: 60,
                      borderRadius: '50%',
                      bgcolor: 'rgba(255, 255, 255, 0.2)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mx: 'auto',
                      mb: 2
                    }}
                  >
                    <EmailIcon sx={{ fontSize: 30 }} />
                  </Box>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Email Proof
                  </Typography>
                  <Typography variant="body2">
                    Send proof from your registered <NAME_EMAIL>
                  </Typography>
                </Grid>
              </Grid>
              
              {/* General Email Submission Button */}
              <Box sx={{ textAlign: 'center', mt: 4 }}>
                <Button
                  variant="contained"
                  size="large"
                  onClick={handleGeneralEmailSubmission}
                  startIcon={<EmailIcon />}
                  sx={{
                    py: 2,
                    px: 4,
                    fontWeight: 'bold',
                    borderRadius: 3,
                    bgcolor: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    border: '2px solid rgba(255, 255, 255, 0.3)',
                    '&:hover': {
                      bgcolor: 'rgba(255, 255, 255, 0.3)',
                      border: '2px solid rgba(255, 255, 255, 0.5)',
                    }
                  }}
                >
                  Submit Claim via Email
                </Button>
              </Box>
            </Paper>
          </Fade>

          {/* Additional Ideas */}
          <Fade in={visible} timeout={1200}>
            <Box>
              <Typography variant="h4" fontWeight="bold" textAlign="center" gutterBottom sx={{ mb: 4 }}>
                More Ways to Earn Pro Time
              </Typography>
              <Typography variant="body1" textAlign="center" color="text.secondary" sx={{ mb: 4 }}>
                These additional promotional ideas can be discussed for custom rewards
              </Typography>
              <Grid container spacing={3}>
                {additionalIdeas.map((idea, index) => (
                  <Grid item xs={12} sm={6} md={4} key={index}>
                    <Card
                      sx={{
                        height: '100%',
                        borderRadius: 2,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: `0 8px 25px ${alpha(idea.color, 0.15)}`
                        }
                      }}
                    >
                      <CardContent sx={{ p: 3 }}>
                        <Box display="flex" alignItems="center" mb={2}>
                          <Box
                            sx={{
                              width: 40,
                              height: 40,
                              borderRadius: '50%',
                              bgcolor: alpha(idea.color, 0.1),
                              color: idea.color,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              mr: 2
                            }}
                          >
                            {idea.icon}
                          </Box>
                          <Typography variant="h6" fontWeight="bold">
                            {idea.title}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {idea.description}
                        </Typography>
                        <Chip
                          label={idea.suggestedReward}
                          size="small"
                          sx={{
                            bgcolor: alpha(idea.color, 0.1),
                            color: idea.color,
                            fontWeight: 'bold'
                          }}
                        />
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Fade>

          {/* Contact Information */}
          <Fade in={visible} timeout={1400}>
            <Box textAlign="center" mt={8}>
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Questions or Custom Proposals?
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                Have ideas for other promotional activities? Contact us to discuss custom rewards!
              </Typography>
              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center" alignItems="center">
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<EmailIcon />}
                  onClick={() => window.open('mailto:<EMAIL>?subject=Promotional Program Inquiry', '_blank')}
                  sx={{
                    py: 1.5,
                    px: 3,
                    fontWeight: 'bold',
                    borderRadius: 2
                  }}
                >
                  Contact Support
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<ShareIcon />}
                  endIcon={<ArrowDownIcon />}
                  onClick={handleShareMenuOpen}
                  sx={{
                    py: 1.5,
                    px: 3,
                    fontWeight: 'bold',
                    borderRadius: 2
                  }}
                >
                  Share Now
                </Button>
                <Menu
                  anchorEl={shareMenuAnchor}
                  open={Boolean(shareMenuAnchor)}
                  onClose={handleShareMenuClose}
                  PaperProps={{
                    sx: {
                      mt: 1,
                      minWidth: 200,
                      borderRadius: 2,
                      boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
                    }
                  }}
                >
                  <MenuItem onClick={() => handleCopyText(notificationText)}>
                    <ListItemIcon>
                      <CopyIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText>
                      {copiedText ? 'Copied!' : 'Copy Text'}
                    </ListItemText>
                  </MenuItem>
                  <MenuItem onClick={() => handleSocialShare('twitter')}>
                    <ListItemIcon>
                      <TwitterIcon fontSize="small" sx={{ color: '#1DA1F2' }} />
                    </ListItemIcon>
                    <ListItemText>Share on Twitter</ListItemText>
                  </MenuItem>
                  <MenuItem onClick={() => handleSocialShare('facebook')}>
                    <ListItemIcon>
                      <FacebookIcon fontSize="small" sx={{ color: '#1877F2' }} />
                    </ListItemIcon>
                    <ListItemText>Share on Facebook</ListItemText>
                  </MenuItem>
                  <MenuItem onClick={() => handleSocialShare('linkedin')}>
                    <ListItemIcon>
                      <LinkedInIcon fontSize="small" sx={{ color: '#0A66C2' }} />
                    </ListItemIcon>
                    <ListItemText>Share on LinkedIn</ListItemText>
                  </MenuItem>
                  <MenuItem onClick={() => handleSocialShare('whatsapp')}>
                    <ListItemIcon>
                      <WhatsAppIcon fontSize="small" sx={{ color: '#25D366' }} />
                    </ListItemIcon>
                    <ListItemText>Share on WhatsApp</ListItemText>
                  </MenuItem>
                </Menu>
              </Stack>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                Email: <EMAIL>
              </Typography>
            </Box>
          </Fade>
        </Container>
      </Box>
    </Box>
  );
};

export default PromotionsPage;