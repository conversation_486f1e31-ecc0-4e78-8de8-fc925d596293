# Email Delivery System Fix Guide

## 🚨 Problem Summary

The guest email verification system shows success messages but users don't receive emails. This is caused by several configuration issues:

1. **Missing Database Configuration**: Required settings for calling Edge Functions
2. **HTTP Extension Not Enabled**: PostgreSQL extension needed for HTTP requests
3. **Environment Variables**: Edge Function environment variables may not be set
4. **Edge Function Deployment**: Function may not be properly deployed

## 🔧 Step-by-Step Fix

### Step 1: Run the Database Migration

First, apply the diagnostic migration:

```bash
# If using Supabase CLI
supabase db push

# Or apply the migration manually in Supabase Dashboard > SQL Editor
```

The migration file `supabase/migrations/20250112000000_fix_email_delivery_system.sql` will:
- Enable the HTTP extension
- Create diagnostic functions
- Improve error handling

### Step 2: Configure Database Settings

**Option A: Using Supabase Dashboard**

1. Go to Supabase Dashboard → Settings → Database
2. Scroll to "Database Settings" 
3. Add these custom parameters:

```sql
-- In the SQL Editor, run these commands:
ALTER DATABASE postgres SET app.supabase_url = 'https://YOUR_PROJECT_REF.supabase.co';
ALTER DATABASE postgres SET app.service_role_key = 'YOUR_SERVICE_ROLE_KEY';
```

**Option B: Using SQL Editor**

1. Go to Supabase Dashboard → SQL Editor
2. Run this query (replace with your actual values):

```sql
-- Replace YOUR_PROJECT_REF with your actual project reference
ALTER DATABASE postgres SET app.supabase_url = 'https://YOUR_PROJECT_REF.supabase.co';

-- Replace YOUR_SERVICE_ROLE_KEY with your actual service role key
-- Find this in: Dashboard → Settings → API → service_role key
ALTER DATABASE postgres SET app.service_role_key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
```

### Step 3: Set Edge Function Environment Variables

**Option A: Using Supabase Dashboard**

1. Go to Supabase Dashboard → Edge Functions
2. Click on "Environment Variables"
3. Add these variables:

| Name | Value | Description |
|------|-------|-------------|
| `RESEND_API_KEY` | `re_your_api_key_here` | Your Resend API key |
| `FROM_EMAIL` | `<EMAIL>` | Verified sender email |

**Option B: Using Supabase CLI**

```bash
supabase secrets set RESEND_API_KEY=re_your_actual_api_key_here
supabase secrets set FROM_EMAIL=<EMAIL>
```

### Step 4: Deploy/Redeploy Edge Function

**Option A: Using Supabase CLI**

```bash
supabase functions deploy send-guest-verification-email
```

**Option B: Using Supabase Dashboard**

1. Go to Edge Functions
2. Create new function: `send-guest-verification-email`
3. Copy code from `supabase/functions/send-guest-verification-email/index.ts`
4. Deploy the function

### Step 5: Run Diagnostics

After completing the above steps, test the system:

```sql
-- Run this in Supabase SQL Editor to diagnose issues
SELECT * FROM public.diagnose_email_system();
```

This will show you the status of each component and what needs to be fixed.

### Step 6: Test Email Delivery

1. Try creating a guest user in your application
2. Check the diagnostic results
3. Monitor Edge Function logs in Supabase Dashboard

## 🔍 Troubleshooting

### Issue: "HTTP extension not found"

**Solution:**
```sql
CREATE EXTENSION IF NOT EXISTS http;
```

### Issue: "app.supabase_url not configured"

**Solution:**
```sql
ALTER DATABASE postgres SET app.supabase_url = 'https://YOUR_PROJECT_REF.supabase.co';
```

### Issue: "RESEND_API_KEY not found"

**Solutions:**
1. Check Edge Function environment variables
2. Verify Resend API key is valid
3. Ensure domain is verified in Resend

### Issue: "Edge Function unreachable"

**Solutions:**
1. Verify function is deployed
2. Check function logs for errors
3. Ensure service role key is correct

## 📊 Verification Steps

### 1. Database Configuration Check

```sql
-- Check if settings are configured
SELECT name, setting FROM pg_settings 
WHERE name IN ('app.supabase_url', 'app.service_role_key');
```

### 2. HTTP Extension Check

```sql
-- Check if HTTP extension is enabled
SELECT * FROM pg_extension WHERE extname = 'http';
```

### 3. Edge Function Test

```sql
-- Test the improved email function
SELECT * FROM public.send_guest_verification_email_v2(
    '<EMAIL>', 
    '123456', 
    gen_random_uuid()
);
```

### 4. Complete System Test

```sql
-- Test complete guest creation flow
SELECT * FROM public.create_guest_user_v2('<EMAIL>');
```

## 🚀 Expected Results

After applying all fixes:

1. **Database diagnostics** should show all components as "CONFIGURED" or "ENABLED"
2. **Edge Function** should be reachable and respond properly
3. **Email delivery** should work and users should receive verification codes
4. **Error messages** should be more descriptive if issues occur

## 📝 Important Notes

- **Service Role Key**: Keep this secret and never expose it in client-side code
- **Resend Domain**: Ensure `datastatpro.com` is verified in your Resend account
- **Rate Limits**: Resend has rate limits; monitor usage
- **Logs**: Check Edge Function logs for detailed error information

## 🔄 Rollback Plan

If issues occur, you can:

1. Use the original functions by calling `create_guest_user` instead of `create_guest_user_v2`
2. Remove the database settings if needed:
   ```sql
   ALTER DATABASE postgres RESET app.supabase_url;
   ALTER DATABASE postgres RESET app.service_role_key;
   ```

## 📞 Support

If you continue to experience issues:

1. Check Supabase Edge Function logs
2. Verify Resend account status and domain verification
3. Run the diagnostic function and share results
4. Check browser network tab for API errors
