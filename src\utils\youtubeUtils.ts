// YouTube link detection and processing utilities

export interface YouTubeVideoInfo {
  videoId: string;
  originalUrl: string;
  thumbnailUrl: string;
  embedUrl: string;
}

/**
 * Extracts YouTube video ID from various YouTube URL formats
 */
export function extractYouTubeVideoId(url: string): string | null {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/|m\.youtube\.com\/watch\?v=)([^&\n?#]+)/,
    /youtube\.com\/watch\?.*v=([^&\n?#]+)/
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
}

/**
 * Checks if a URL is a YouTube link
 */
export function isYouTubeUrl(url: string): boolean {
  return extractYouTubeVideoId(url) !== null;
}

/**
 * Extracts YouTube video information from a URL
 */
export function getYouTubeVideoInfo(url: string): YouTubeVideoInfo | null {
  const videoId = extractYouTubeVideoId(url);
  
  if (!videoId) {
    return null;
  }

  return {
    videoId,
    originalUrl: url,
    thumbnailUrl: `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`,
    embedUrl: `https://www.youtube.com/embed/${videoId}`
  };
}

/**
 * Detects and extracts all YouTube links from text
 */
export function findYouTubeLinks(text: string): Array<{ url: string; info: YouTubeVideoInfo }> {
  const urlRegex = /https?:\/\/(?:www\.|m\.)?(?:youtube\.com\/(?:watch\?v=|embed\/|v\/)|youtu\.be\/)[\w-]+(?:\S+)?/g;
  const matches = text.match(urlRegex) || [];
  
  return matches
    .map(url => {
      const info = getYouTubeVideoInfo(url);
      return info ? { url, info } : null;
    })
    .filter((item): item is { url: string; info: YouTubeVideoInfo } => item !== null);
}

/**
 * Replaces YouTube URLs in text with clickable links
 * Returns an array of text segments and link components
 */
export function parseTextWithYouTubeLinks(text: string): Array<{ type: 'text' | 'youtube'; content: string; info?: YouTubeVideoInfo }> {
  const youtubeLinks = findYouTubeLinks(text);
  
  if (youtubeLinks.length === 0) {
    return [{ type: 'text', content: text }];
  }

  const segments: Array<{ type: 'text' | 'youtube'; content: string; info?: YouTubeVideoInfo }> = [];
  let lastIndex = 0;

  youtubeLinks.forEach(({ url, info }) => {
    const urlIndex = text.indexOf(url, lastIndex);
    
    // Add text before the URL
    if (urlIndex > lastIndex) {
      segments.push({
        type: 'text',
        content: text.substring(lastIndex, urlIndex)
      });
    }
    
    // Add the YouTube link
    segments.push({
      type: 'youtube',
      content: url,
      info
    });
    
    lastIndex = urlIndex + url.length;
  });

  // Add remaining text after the last URL
  if (lastIndex < text.length) {
    segments.push({
      type: 'text',
      content: text.substring(lastIndex)
    });
  }

  return segments;
}

// Re-export from richTextUtils for backward compatibility
export { parseTextWithYouTubeLinks as parseTextWithYouTubeLinksCompat } from './richTextUtils';
