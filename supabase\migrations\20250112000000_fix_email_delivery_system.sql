-- Fix Email Delivery System
-- This migration addresses the email delivery issues in the guest verification system

-- Step 1: Enable the http extension (required for calling Edge Functions from database)
CREATE EXTENSION IF NOT EXISTS http;

-- Step 2: Set up database configuration parameters
-- These are required for the email sending function to work properly

-- Note: These values need to be set by the database administrator
-- You can set them using the Supabase dashboard or SQL commands like:
-- ALTER DATABASE postgres SET app.supabase_url = 'https://your-project-ref.supabase.co';
-- ALTER DATABASE postgres SET app.service_role_key = 'your-service-role-key';

-- Step 3: Create a diagnostic function to check the email system configuration
CREATE OR REPLACE FUNCTION public.diagnose_email_system()
RETURNS TABLE(
    component TEXT,
    status TEXT,
    details TEXT,
    action_required TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_supabase_url TEXT;
    v_service_role_key TEXT;
    v_http_extension_exists BOOLEAN;
    v_edge_function_test TEXT;
    v_test_response TEXT;
    v_test_status INTEGER;
BEGIN
    -- Check if http extension is enabled
    SELECT EXISTS(
        SELECT 1 FROM pg_extension WHERE extname = 'http'
    ) INTO v_http_extension_exists;
    
    IF v_http_extension_exists THEN
        RETURN QUERY SELECT 
            'HTTP Extension'::TEXT,
            'ENABLED'::TEXT,
            'PostgreSQL http extension is installed and enabled'::TEXT,
            'None'::TEXT;
    ELSE
        RETURN QUERY SELECT 
            'HTTP Extension'::TEXT,
            'MISSING'::TEXT,
            'PostgreSQL http extension is not enabled'::TEXT,
            'Run: CREATE EXTENSION IF NOT EXISTS http;'::TEXT;
        RETURN;
    END IF;
    
    -- Check database configuration settings
    BEGIN
        v_supabase_url := current_setting('app.supabase_url');
        RETURN QUERY SELECT 
            'Supabase URL'::TEXT,
            'CONFIGURED'::TEXT,
            'app.supabase_url is set to: ' || v_supabase_url::TEXT,
            'None'::TEXT;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN QUERY SELECT 
                'Supabase URL'::TEXT,
                'MISSING'::TEXT,
                'app.supabase_url is not configured'::TEXT,
                'Set using: ALTER DATABASE postgres SET app.supabase_url = ''https://your-project-ref.supabase.co'';'::TEXT;
    END;
    
    BEGIN
        v_service_role_key := current_setting('app.service_role_key');
        RETURN QUERY SELECT 
            'Service Role Key'::TEXT,
            'CONFIGURED'::TEXT,
            'app.service_role_key is configured (length: ' || length(v_service_role_key)::TEXT || ' chars)'::TEXT,
            'None'::TEXT;
    EXCEPTION
        WHEN OTHERS THEN
            RETURN QUERY SELECT 
                'Service Role Key'::TEXT,
                'MISSING'::TEXT,
                'app.service_role_key is not configured'::TEXT,
                'Set using: ALTER DATABASE postgres SET app.service_role_key = ''your-service-role-key'';'::TEXT;
    END;
    
    -- Test Edge Function connectivity (only if all prerequisites are met)
    IF v_http_extension_exists AND v_supabase_url IS NOT NULL AND v_service_role_key IS NOT NULL THEN
        BEGIN
            -- Test the Edge Function with a dummy request
            SELECT 
                content::TEXT,
                status_code
            INTO 
                v_test_response,
                v_test_status
            FROM 
                http((
                    'POST',
                    v_supabase_url || '/functions/v1/send-guest-verification-email',
                    ARRAY[
                        http_header('Authorization', 'Bearer ' || v_service_role_key),
                        http_header('Content-Type', 'application/json')
                    ],
                    'application/json',
                    '{"email":"<EMAIL>","verificationCode":"123456","guestId":"00000000-0000-0000-0000-000000000000"}'
                ));
            
            IF v_test_status = 400 THEN
                -- 400 is expected for test data, means function is reachable
                RETURN QUERY SELECT 
                    'Edge Function'::TEXT,
                    'REACHABLE'::TEXT,
                    'Edge Function responded with status: ' || v_test_status::TEXT,
                    'Check Edge Function environment variables (RESEND_API_KEY, FROM_EMAIL)'::TEXT;
            ELSIF v_test_status = 500 THEN
                RETURN QUERY SELECT 
                    'Edge Function'::TEXT,
                    'ERROR'::TEXT,
                    'Edge Function error: ' || v_test_response::TEXT,
                    'Check Edge Function logs and environment variables'::TEXT;
            ELSE
                RETURN QUERY SELECT 
                    'Edge Function'::TEXT,
                    'UNKNOWN'::TEXT,
                    'Unexpected response status: ' || v_test_status::TEXT || ', Response: ' || v_test_response::TEXT,
                    'Check Edge Function deployment and logs'::TEXT;
            END IF;
            
        EXCEPTION
            WHEN OTHERS THEN
                RETURN QUERY SELECT 
                    'Edge Function'::TEXT,
                    'UNREACHABLE'::TEXT,
                    'Failed to connect to Edge Function: ' || SQLERRM::TEXT,
                    'Check Edge Function deployment and network connectivity'::TEXT;
        END;
    ELSE
        RETURN QUERY SELECT 
            'Edge Function'::TEXT,
            'SKIPPED'::TEXT,
            'Cannot test Edge Function due to missing prerequisites'::TEXT,
            'Fix the above issues first'::TEXT;
    END IF;
    
END;
$$;

-- Step 4: Create an improved email sending function with better error handling
CREATE OR REPLACE FUNCTION public.send_guest_verification_email_v2(
    p_email VARCHAR(255),
    p_verification_code VARCHAR(255),
    p_guest_id UUID
) RETURNS TABLE(
    success BOOLEAN,
    error_message TEXT,
    http_status INTEGER,
    response_body TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_response TEXT;
    v_status_code INTEGER;
    v_supabase_url TEXT;
    v_service_role_key TEXT;
BEGIN
    -- Check prerequisites
    BEGIN
        v_supabase_url := current_setting('app.supabase_url');
        v_service_role_key := current_setting('app.service_role_key');
    EXCEPTION
        WHEN OTHERS THEN
            RETURN QUERY SELECT 
                FALSE,
                'Database configuration missing: ' || SQLERRM,
                0,
                NULL::TEXT;
            RETURN;
    END;
    
    -- Call the Edge Function to send email
    BEGIN
        SELECT 
            content::TEXT,
            status_code
        INTO 
            v_response,
            v_status_code
        FROM 
            http((
                'POST',
                v_supabase_url || '/functions/v1/send-guest-verification-email',
                ARRAY[
                    http_header('Authorization', 'Bearer ' || v_service_role_key),
                    http_header('Content-Type', 'application/json')
                ],
                'application/json',
                json_build_object(
                    'email', p_email,
                    'verificationCode', p_verification_code,
                    'guestId', p_guest_id
                )::TEXT
            ));
        
        -- Return detailed response
        RETURN QUERY SELECT 
            (v_status_code = 200),
            CASE 
                WHEN v_status_code = 200 THEN 'Email sent successfully'
                ELSE 'Email sending failed'
            END,
            v_status_code,
            v_response;
            
    EXCEPTION
        WHEN OTHERS THEN
            RETURN QUERY SELECT 
                FALSE,
                'HTTP request failed: ' || SQLERRM,
                0,
                NULL::TEXT;
    END;
END;
$$;

-- Step 5: Update the create_guest_user function to use the improved email function
CREATE OR REPLACE FUNCTION public.create_guest_user_v2(
    p_email VARCHAR(255),
    p_full_name VARCHAR(255) DEFAULT NULL,
    p_institution VARCHAR(255) DEFAULT NULL,
    p_marketing_consent BOOLEAN DEFAULT FALSE
) RETURNS TABLE(
    guest_id UUID,
    verification_token VARCHAR(255),
    success BOOLEAN,
    message TEXT,
    email_sent BOOLEAN,
    email_error TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_guest_id UUID;
    v_token VARCHAR(255);
    v_existing_user UUID;
    v_email_result RECORD;
BEGIN
    -- Check if email already exists and is verified
    SELECT id INTO v_existing_user 
    FROM public.guest_users 
    WHERE email = p_email AND email_verified = TRUE;
    
    IF v_existing_user IS NOT NULL THEN
        RETURN QUERY SELECT 
            v_existing_user,
            NULL::VARCHAR(255),
            FALSE,
            'Email already verified. Please use the login option.'::TEXT,
            FALSE,
            NULL::TEXT;
        RETURN;
    END IF;
    
    -- Check if email exists but is not verified (allow resending)
    SELECT id INTO v_existing_user 
    FROM public.guest_users 
    WHERE email = p_email AND email_verified = FALSE;
    
    IF v_existing_user IS NOT NULL THEN
        -- Update existing unverified user with new token
        v_token := LPAD(FLOOR(RANDOM() * 1000000)::TEXT, 6, '0');
        
        UPDATE public.guest_users 
        SET 
            verification_token = v_token,
            verification_expires_at = NOW() + INTERVAL '24 hours',
            updated_at = NOW()
        WHERE id = v_existing_user;
        
        v_guest_id := v_existing_user;
    ELSE
        -- Generate verification token (6-digit code for user-friendly input)
        v_token := LPAD(FLOOR(RANDOM() * 1000000)::TEXT, 6, '0');
        
        -- Insert new guest user
        INSERT INTO public.guest_users (
            email, 
            verification_token, 
            verification_expires_at,
            marketing_consent,
            full_name,
            institution
        ) VALUES (
            p_email, 
            v_token, 
            NOW() + INTERVAL '24 hours',
            p_marketing_consent,
            p_full_name,
            p_institution
        ) RETURNING id INTO v_guest_id;
    END IF;
    
    -- Try to send verification email using improved function
    SELECT * INTO v_email_result 
    FROM public.send_guest_verification_email_v2(p_email, v_token, v_guest_id);
    
    RETURN QUERY SELECT 
        v_guest_id,
        v_token,
        TRUE,
        CASE 
            WHEN v_email_result.success THEN 'Guest user created and verification email sent successfully'
            ELSE 'Guest user created but email sending failed: ' || COALESCE(v_email_result.error_message, 'Unknown error')
        END::TEXT,
        v_email_result.success,
        v_email_result.error_message;
        
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT 
            NULL::UUID,
            NULL::VARCHAR(255),
            FALSE,
            'Failed to create guest user: ' || SQLERRM::TEXT,
            FALSE,
            SQLERRM::TEXT;
END;
$$;

-- Step 6: Add helpful comments and documentation
COMMENT ON FUNCTION public.diagnose_email_system() IS 'Diagnostic function to check email system configuration and identify issues';
COMMENT ON FUNCTION public.send_guest_verification_email_v2(VARCHAR, VARCHAR, UUID) IS 'Improved email sending function with detailed error reporting';
COMMENT ON FUNCTION public.create_guest_user_v2(VARCHAR, VARCHAR, VARCHAR, BOOLEAN) IS 'Improved guest user creation with better email handling and error reporting';
