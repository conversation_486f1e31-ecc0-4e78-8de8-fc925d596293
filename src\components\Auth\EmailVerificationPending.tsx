import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Alert,
  Paper,
  useTheme,
  CircularProgress,
  Divider,
  LinearProgress
} from '@mui/material';
import { 
  Email as EmailIcon, 
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { GuestUserStatus } from '../../types';

interface EmailVerificationPendingProps {
  email: string;
  onVerificationComplete?: () => void;
  onChangeEmail?: () => void;
  onResendCode?: () => void;
}

const EmailVerificationPending: React.FC<EmailVerificationPendingProps> = ({
  email,
  onVerificationComplete,
  onChangeEmail,
  onResendCode
}) => {
  const theme = useTheme();
  const { guestUser, createGuestUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [timeLeft, setTimeLeft] = useState(60); // 60 seconds cooldown for resend
  const [canResend, setCanResend] = useState(false);

  // Countdown timer for resend button
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [timeLeft]);

  // Check if guest user is verified
  useEffect(() => {
    if (guestUser?.status === GuestUserStatus.VERIFIED) {
      onVerificationComplete?.();
    }
  }, [guestUser, onVerificationComplete]);

  const handleResendCode = async () => {
    setError(null);
    setSuccess(null);
    setLoading(true);

    try {
      const response = await createGuestUser({ email });
      if (response.success) {
        setSuccess('New verification code sent!');
        setTimeLeft(60);
        setCanResend(false);
        onResendCode?.();
      } else {
        setError(response.error || 'Failed to resend verification code');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to resend verification code');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Paper elevation={3} sx={{ p: 4, maxWidth: 450, mx: 'auto', mt: 4 }}>      
      <Box display="flex" flexDirection="column" alignItems="center" mb={3}>
        <Box position="relative" display="inline-flex" mb={2}>
          <CircularProgress
            variant="indeterminate"
            size={60}
            thickness={4}
            sx={{ color: theme.palette.primary.main }}
          />
          <Box
            position="absolute"
            top={0}
            left={0}
            bottom={0}
            right={0}
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <EmailIcon fontSize="large" color="primary" />
          </Box>
        </Box>
        
        <Typography variant="h5" component="h1" gutterBottom textAlign="center">
          Check Your Email
        </Typography>
        
        <Typography variant="body1" color="text.secondary" textAlign="center">
          We've sent a verification code to
        </Typography>
        
        <Typography variant="h6" color="primary" textAlign="center" sx={{ mt: 1 }}>
          {email}
        </Typography>
      </Box>

      <Alert 
        severity="info" 
        icon={<ScheduleIcon />}
        sx={{ 
          mb: 3, 
          border: `1px solid ${theme.palette.info.main}`,
          '& .MuiAlert-message': { width: '100%' }
        }}
      >
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          What's Next?
        </Typography>
        <Typography variant="body2" component="ol" sx={{ pl: 2, m: 0 }}>
          <li>Check your email inbox (and spam folder)</li>
          <li>Find the email from DataStatPro</li>
          <li>Enter the 6-digit verification code</li>
          <li>Start using enhanced guest features!</li>
        </Typography>
      </Alert>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      {/* Progress indicator */}
      <Box sx={{ mb: 3 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
          <Typography variant="body2" color="text.secondary">
            Verification Status
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {guestUser?.status === GuestUserStatus.PENDING ? 'Waiting...' : 'Processing...'}
          </Typography>
        </Box>
        <LinearProgress 
          variant="indeterminate" 
          sx={{ 
            height: 6, 
            borderRadius: 3,
            backgroundColor: theme.palette.grey[200],
            '& .MuiLinearProgress-bar': {
              borderRadius: 3
            }
          }} 
        />
      </Box>

      {/* Action buttons */}
      <Box display="flex" flexDirection="column" gap={2}>
        <Button
          fullWidth
          variant="outlined"
          color="primary"
          size="large"
          onClick={handleResendCode}
          disabled={loading || !canResend}
          startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
          sx={{ 
            py: 1.5,
            fontSize: '1rem',
            fontWeight: 'medium'
          }}
        >
          {loading ? 'Sending...' : canResend ? 'Resend Code' : `Resend in ${formatTime(timeLeft)}`}
        </Button>

        <Divider sx={{ my: 1 }} />

        <Box display="flex" justifyContent="space-between" gap={2}>
          <Button
            variant="text"
            onClick={onChangeEmail}
            disabled={loading}
            sx={{ 
              textTransform: 'none',
              flex: 1
            }}
          >
            Change Email
          </Button>
          
          <Button
            variant="text"
            onClick={() => window.location.reload()}
            disabled={loading}
            sx={{ 
              textTransform: 'none',
              flex: 1
            }}
          >
            Refresh Page
          </Button>
        </Box>
      </Box>

      {/* Help text */}
      <Alert 
        severity="warning" 
        sx={{ 
          mt: 3,
          backgroundColor: theme.palette.warning.light + '20',
          border: `1px solid ${theme.palette.warning.light}`,
          '& .MuiAlert-message': { width: '100%' }
        }}
      >
        <Typography variant="body2">
          <strong>Didn't receive the email?</strong>
        </Typography>
        <Typography variant="body2" component="ul" sx={{ pl: 2, mt: 1, mb: 0 }}>
          <li>Check your spam/junk folder</li>
          <li>Make sure you entered the correct email</li>
          <li>Wait a few minutes for delivery</li>
          <li>Try resending the code</li>
        </Typography>
      </Alert>

      <Typography variant="caption" display="block" textAlign="center" sx={{ mt: 3, color: 'text.secondary' }}>
        The verification code expires in 15 minutes for security.
      </Typography>
    </Paper>
  );
};

export default EmailVerificationPending;