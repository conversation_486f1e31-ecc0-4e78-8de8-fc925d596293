import React, { Suspense, lazy } from 'react'; // Import lazy
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, CircularProgress } from '@mui/material';
import App from './App';

// Lazy load pages to improve initial bundle size
const LandingPage = lazy(() => import('./pages/LandingPage'));
const PricingPage = lazy(() => import('./pages/PricingPage'));
const PricingDevPage = lazy(() => import('./pages/PricingDevPage'));
const PromotionsPage = lazy(() => import('./pages/PromotionsPage'));
const PrivacyPolicyPage = lazy(() => import('./pages/PrivacyPolicyPage'));
const TermsOfServicePage = lazy(() => import('./pages/TermsOfServicePage'));

// PostHocTestsPage is lazy loaded in App.tsx where it's actually used
// Import other publication ready pages as needed (SMDTable, Table1a)
// Removed imports for TTest pages as routing is handled in App.tsx


const AppRouter: React.FC = () => {

  return (
    <Suspense
      fallback={
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100vh',
            width: '100%'
          }}
        >
          <CircularProgress />
        </Box>
      }
    >
      <Routes>
        <Route path="/" element={<LandingPage />} />
        <Route path="/pricing" element={<PricingPage />} />
        <Route path="/pricing-dev" element={<PricingDevPage />} />
        <Route path="/promotions" element={<PromotionsPage />} />
        <Route path="/privacy" element={<PrivacyPolicyPage />} />
        <Route path="/terms" element={<TermsOfServicePage />} />

        <Route path="/auth/login" element={<Navigate to="/app/auth" replace />} />
        <Route path="/app/*" element={<App />} />
          {/* All /app routes are handled internally by App.tsx */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Suspense>
  );
};

export default AppRouter;
