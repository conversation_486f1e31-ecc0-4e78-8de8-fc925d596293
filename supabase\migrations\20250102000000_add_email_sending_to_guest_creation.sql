-- Migration to add email sending functionality to guest user creation
-- This updates the create_guest_user function to call the email service

-- First, create a function to call the Edge Function for sending emails
CREATE OR REPLACE FUNCTION public.send_guest_verification_email(
    p_email VARCHAR(255),
    p_verification_code VARCHAR(255),
    p_guest_id UUID
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_response TEXT;
    v_status_code INTEGER;
BEGIN
    -- Call the Edge Function to send email
    -- Note: This requires the http extension to be enabled
    SELECT 
        content::TEXT,
        status_code
    INTO 
        v_response,
        v_status_code
    FROM 
        http((
            'POST',
            current_setting('app.supabase_url') || '/functions/v1/send-guest-verification-email',
            ARRAY[
                http_header('Authorization', 'Bearer ' || current_setting('app.service_role_key')),
                http_header('Content-Type', 'application/json')
            ],
            'application/json',
            json_build_object(
                'email', p_email,
                'verificationCode', p_verification_code,
                'guestId', p_guest_id
            )::TEXT
        ));
    
    -- Return true if email was sent successfully (status 200)
    RETURN v_status_code = 200;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error but don't fail the guest creation
        RAISE WARNING 'Failed to send verification email: %', SQLERRM;
        RETURN FALSE;
END;
$$;

-- Drop the existing create_guest_user function
DROP FUNCTION IF EXISTS public.create_guest_user(VARCHAR, VARCHAR, VARCHAR, BOOLEAN);

-- Create the updated create_guest_user function with email sending
CREATE OR REPLACE FUNCTION public.create_guest_user(
    p_email VARCHAR(255),
    p_full_name VARCHAR(255) DEFAULT NULL,
    p_institution VARCHAR(255) DEFAULT NULL,
    p_marketing_consent BOOLEAN DEFAULT FALSE
) RETURNS TABLE(
    guest_id UUID,
    verification_token VARCHAR(255),
    success BOOLEAN,
    message TEXT,
    email_sent BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_guest_id UUID;
    v_token VARCHAR(255);
    v_existing_user UUID;
    v_email_sent BOOLEAN := FALSE;
BEGIN
    -- Check if email already exists
    SELECT id INTO v_existing_user 
    FROM public.guest_users 
    WHERE email = p_email;
    
    IF v_existing_user IS NOT NULL THEN
        RETURN QUERY SELECT 
            v_existing_user,
            NULL::VARCHAR(255),
            FALSE,
            'Email already registered'::TEXT,
            FALSE;
        RETURN;
    END IF;
    
    -- Generate verification token (6-digit code for user-friendly input)
    v_token := LPAD(FLOOR(RANDOM() * 1000000)::TEXT, 6, '0');
    
    -- Insert guest user
    INSERT INTO public.guest_users (
        email, 
        verification_token, 
        verification_expires_at,
        marketing_consent,
        full_name,
        institution
    ) VALUES (
        p_email, 
        v_token, 
        NOW() + INTERVAL '24 hours',
        p_marketing_consent,
        p_full_name,
        p_institution
    ) RETURNING id INTO v_guest_id;
    
    -- Try to send verification email
    BEGIN
        v_email_sent := public.send_guest_verification_email(p_email, v_token, v_guest_id);
    EXCEPTION
        WHEN OTHERS THEN
            -- Log warning but don't fail the guest creation
            RAISE WARNING 'Email sending failed: %', SQLERRM;
            v_email_sent := FALSE;
    END;
    
    RETURN QUERY SELECT 
        v_guest_id,
        v_token,
        TRUE,
        CASE 
            WHEN v_email_sent THEN 'Guest user created and verification email sent successfully'
            ELSE 'Guest user created but email sending failed'
        END::TEXT,
        v_email_sent;
        
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT 
            NULL::UUID,
            NULL::VARCHAR(255),
            FALSE,
            SQLERRM::TEXT,
            FALSE;
END;
$$;

-- Drop the existing verify_guest_email function
DROP FUNCTION IF EXISTS public.verify_guest_email(VARCHAR);

-- Update the verify_guest_email function to work with email and verification code
CREATE OR REPLACE FUNCTION public.verify_guest_email(
    p_email VARCHAR(255),
    p_verification_code VARCHAR(255)
) RETURNS TABLE(
    guest_id UUID,
    email VARCHAR(255),
    success BOOLEAN,
    message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_guest_record RECORD;
BEGIN
    -- Find and verify the token using both email and verification code
    UPDATE public.guest_users 
    SET 
        email_verified = TRUE,
        verification_token = NULL,
        verification_expires_at = NULL,
        updated_at = NOW()
    WHERE 
        email = p_email
        AND verification_token = p_verification_code
        AND verification_expires_at > NOW()
        AND email_verified = FALSE
    RETURNING id, email INTO v_guest_record;
    
    IF v_guest_record.id IS NOT NULL THEN
        RETURN QUERY SELECT 
            v_guest_record.id,
            v_guest_record.email,
            TRUE,
            'Email verified successfully'::TEXT;
    ELSE
        RETURN QUERY SELECT 
            NULL::UUID,
            NULL::VARCHAR(255),
            FALSE,
            'Invalid or expired verification code'::TEXT;
    END IF;
END;
$$;

-- Add missing columns to guest_users table if they don't exist
DO $$
BEGIN
    -- Add full_name column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'guest_users' AND column_name = 'full_name') THEN
        ALTER TABLE public.guest_users ADD COLUMN full_name VARCHAR(255);
    END IF;
    
    -- Add institution column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'guest_users' AND column_name = 'institution') THEN
        ALTER TABLE public.guest_users ADD COLUMN institution VARCHAR(255);
    END IF;
END
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.send_guest_verification_email(VARCHAR, VARCHAR, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.send_guest_verification_email(VARCHAR, VARCHAR, UUID) TO anon;
GRANT EXECUTE ON FUNCTION public.create_guest_user(VARCHAR, VARCHAR, VARCHAR, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_guest_user(VARCHAR, VARCHAR, VARCHAR, BOOLEAN) TO anon;
GRANT EXECUTE ON FUNCTION public.verify_guest_email(VARCHAR, VARCHAR) TO authenticated;
GRANT EXECUTE ON FUNCTION public.verify_guest_email(VARCHAR, VARCHAR) TO anon;

-- Add comments
COMMENT ON FUNCTION public.send_guest_verification_email(VARCHAR, VARCHAR, UUID) IS 'Sends verification email to guest users via Edge Function';
COMMENT ON FUNCTION public.create_guest_user(VARCHAR, VARCHAR, VARCHAR, BOOLEAN) IS 'Creates guest user and sends verification email';
COMMENT ON FUNCTION public.verify_guest_email(VARCHAR, VARCHAR) IS 'Verifies guest email using email and verification code';