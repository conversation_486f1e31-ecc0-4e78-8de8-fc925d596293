import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  Alert,
  CircularProgress,
  useTheme,
  alpha,
  IconButton,
  Tooltip,
  Zoom,
  useMediaQuery,
  Chip,
  Stack,
  <PERSON>read<PERSON><PERSON><PERSON>,
  Link
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  BarChart as BarChartIcon,
  Security as SecurityIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit,
  CardMembership as SubscriptionIcon,
  Storage as StorageIcon,
  AdminPanelSettings as AdminIcon,
  MonitorHeart as MonitoringIcon,
  Analytics as AnalyticsIcon,
  Backup as BackupIcon,
  Api as ApiIcon,
  History as AuditIcon,
  Home as HomeIcon,
  NavigateNext as NavigateNextIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';

interface AdminOption {
  id: string;
  title: string;
  description: string;
  icon: React.ReactElement;
  route: string;
  category: 'core' | 'management' | 'analytics' | 'security' | 'system';
  priority: number;
  color: string;
  available: boolean;
}

const AdminMainPage: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { isAdmin, canAccessAdminDashboard, loading, user } = useAuth();
  const [pageLoading, setPageLoading] = useState(true);
  const [isMaximized, setIsMaximized] = useState(false);
  
  // Responsive breakpoints
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  const isLargeScreen = useMediaQuery(theme.breakpoints.up('lg'));

  // Check admin access on component mount
  useEffect(() => {
    if (!loading) {
      if (!user) {
        navigate('/auth');
        return;
      }

      if (!canAccessAdminDashboard) {
        navigate('/app/dashboard');
        return;
      }

      setPageLoading(false);
    }
  }, [loading, user, canAccessAdminDashboard, navigate]);

  // Function to handle maximize/minimize toggle
  const handleToggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  // Keyboard shortcut for maximize toggle (F11 or Ctrl/Cmd + Shift + F)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F11' ||
          ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'F')) {
        event.preventDefault();
        handleToggleMaximize();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isMaximized]);

  // Handle body overflow when maximized
  useEffect(() => {
    if (isMaximized) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isMaximized]);

  // Admin options configuration
  const adminOptions: AdminOption[] = [
    {
      id: 'overview',
      title: 'Admin Overview',
      description: 'System overview, key metrics, and dashboard summary',
      icon: <DashboardIcon />,
      route: 'admin/overview',
      category: 'core',
      priority: 1,
      color: theme.palette.primary.main,
      available: true
    },
    {
      id: 'users',
      title: 'User Management',
      description: 'Manage users, roles, permissions, and account settings',
      icon: <PeopleIcon />,
      route: 'admin/users',
      category: 'management',
      priority: 2,
      color: theme.palette.info.main,
      available: true
    },
    {
      id: 'quotas',
      title: 'Dataset Quota Management',
      description: 'Manage storage quotas, dataset limits, and usage monitoring',
      icon: <StorageIcon />,
      route: 'admin/dataset-quota',
      category: 'management',
      priority: 3,
      color: theme.palette.warning.main,
      available: true
    },
    {
      id: 'subscriptions',
      title: 'Subscription Overrides',
      description: 'Manage subscription plans, billing, and account upgrades',
      icon: <SubscriptionIcon />,
      route: 'admin/subscriptions',
      category: 'management',
      priority: 4,
      color: theme.palette.success.main,
      available: false
    },
    {
      id: 'statistics',
      title: 'System Statistics',
      description: 'View system performance, usage analytics, and reports',
      icon: <BarChartIcon />,
      route: 'admin/statistics',
      category: 'analytics',
      priority: 5,
      color: theme.palette.secondary.main,
      available: false
    },
    {
      id: 'analytics',
      title: 'Analytics Dashboard',
      description: 'Advanced analytics, user behavior, and system insights',
      icon: <AnalyticsIcon />,
      route: 'admin/analytics',
      category: 'analytics',
      priority: 6,
      color: '#9c27b0',
      available: true
    },
    {
      id: 'notifications',
      title: 'Notification Manager',
      description: 'Send notifications, manage alerts, and communication',
      icon: <NotificationsIcon />,
      route: 'admin/notifications',
      category: 'management',
      priority: 7,
      color: '#ff9800',
      available: false
    },
    {
      id: 'security',
      title: 'Security Settings',
      description: 'Security policies, access controls, and audit settings',
      icon: <SecurityIcon />,
      route: 'admin/security',
      category: 'security',
      priority: 8,
      color: '#f44336',
      available: true
    },
    {
      id: 'monitoring',
      title: 'System Monitoring',
      description: 'Monitor system health, performance, and uptime',
      icon: <MonitoringIcon />,
      route: 'admin/monitoring',
      category: 'system',
      priority: 9,
      color: '#4caf50',
      available: true
    },
    {
      id: 'backup',
      title: 'Backup & Recovery',
      description: 'Data backup, recovery options, and system maintenance',
      icon: <BackupIcon />,
      route: 'admin/backup',
      category: 'system',
      priority: 10,
      color: '#607d8b',
      available: true
    },
    {
      id: 'api',
      title: 'API Management',
      description: 'API keys, rate limits, and integration management',
      icon: <ApiIcon />,
      route: 'admin/api',
      category: 'system',
      priority: 11,
      color: '#795548',
      available: true
    },
    {
      id: 'audit',
      title: 'Audit Logs',
      description: 'View system logs, user activities, and security events',
      icon: <AuditIcon />,
      route: 'admin/audit',
      category: 'security',
      priority: 12,
      color: '#3f51b5',
      available: true
    },
    {
      id: 'settings',
      title: 'Admin Settings',
      description: 'System configuration, preferences, and admin tools',
      icon: <SettingsIcon />,
      route: 'admin/settings',
      category: 'core',
      priority: 13,
      color: '#9e9e9e',
      available: true
    }
  ];

  // Filter and sort admin options
  const availableOptions = adminOptions
    .filter(option => option.available)
    .sort((a, b) => a.priority - b.priority);

  // Group options by category
  const groupedOptions = availableOptions.reduce((groups, option) => {
    const category = option.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(option);
    return groups;
  }, {} as Record<string, AdminOption[]>);

  // Category display names and colors
  const categoryInfo = {
    core: { name: 'Core Administration', color: theme.palette.primary.main },
    management: { name: 'User & Resource Management', color: theme.palette.info.main },
    analytics: { name: 'Analytics & Reporting', color: theme.palette.secondary.main },
    security: { name: 'Security & Compliance', color: theme.palette.error.main },
    system: { name: 'System & Maintenance', color: theme.palette.success.main }
  };

  // Handle option click
  const handleOptionClick = (option: AdminOption) => {
    // Ensure proper /app prefix for admin routes
    const route = option.route.startsWith('/') ? option.route : `/${option.route}`;
    const fullRoute = route.startsWith('/app/') ? route : `/app${route}`;
    navigate(fullRoute);
  };

  // Handle navigation to dashboard
  const handleNavigateToDashboard = () => {
    navigate('/app/dashboard');
  };

  // Show loading while checking authentication
  if (loading || pageLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={48} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading Admin Dashboard...
          </Typography>
        </Box>
      </Container>
    );
  }

  // Show access denied if not admin
  if (!isAdmin || !canAccessAdminDashboard) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Access Denied
          </Typography>
          <Typography>
            You do not have permission to access the admin dashboard. Admin privileges are required.
          </Typography>
        </Alert>
      </Container>
    );
  }

  return (
    <Box
      sx={{
        width: '100%',
        minHeight: '100vh',
        bgcolor: 'background.default',
        position: isMaximized ? 'fixed' : 'relative',
        top: isMaximized ? 0 : 'auto',
        left: isMaximized ? 0 : 'auto',
        right: isMaximized ? 0 : 'auto',
        bottom: isMaximized ? 0 : 'auto',
        zIndex: isMaximized ? 1300 : 'auto',
        transition: 'all 0.3s ease-in-out',
        overflow: isMaximized ? 'auto' : 'visible'
      }}
    >
      <Container
        maxWidth={isMaximized ? false : "xl"}
        sx={{
          py: 3,
          px: { xs: 2, sm: 3 },
          maxWidth: isMaximized ? '100%' : undefined,
          width: isMaximized ? '100%' : undefined,
          height: isMaximized ? '100vh' : 'auto'
        }}
      >
        {/* Header Section */}
        <Box sx={{ mb: 4 }}>
          {/* Breadcrumb Navigation */}
          <Breadcrumbs
            aria-label="breadcrumb"
            separator={<NavigateNextIcon fontSize="small" />}
            sx={{ mb: 2 }}
          >
            <Link
              component="button"
              variant="body2"
              onClick={handleNavigateToDashboard}
              sx={{
                display: 'flex',
                alignItems: 'center',
                textDecoration: 'none',
                color: 'text.secondary',
                '&:hover': {
                  color: 'primary.main',
                  textDecoration: 'underline'
                }
              }}
            >
              <HomeIcon sx={{ mr: 0.5, fontSize: 16 }} />
              Dashboard
            </Link>
            <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
              <AdminIcon sx={{ mr: 0.5, fontSize: 16 }} />
              Admin Panel
            </Typography>
          </Breadcrumbs>

          {/* Header with Title and Maximize Button */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box>
              <Typography
                variant={isMaximized ? 'h3' : isMobile ? 'h5' : 'h4'}
                component="h1"
                sx={{
                  fontWeight: 700,
                  color: 'text.primary',
                  mb: 1,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2
                }}
              >
                <AdminIcon sx={{ fontSize: isMaximized ? 48 : isMobile ? 32 : 40 }} />
                Admin Control Panel
                {isMaximized && (
                  <Chip
                    label="MAXIMIZED"
                    size="small"
                    sx={{
                      bgcolor: alpha(theme.palette.primary.main, 0.1),
                      color: 'primary.main',
                      fontWeight: 600,
                      fontSize: '0.75rem'
                    }}
                  />
                )}
              </Typography>
              <Typography
                variant="body1"
                color="text.secondary"
                sx={{ maxWidth: isMaximized ? '60%' : '100%' }}
              >
                Comprehensive system administration and management tools. Select an option below to access specific admin functions.
              </Typography>
            </Box>

            {/* Enhanced Maximize/Minimize Button */}
            <Tooltip
              title={isMaximized ? 'Exit Fullscreen (F11)' : 'Enter Fullscreen (F11)'}
              placement="left"
              TransitionComponent={Zoom}
            >
              <IconButton
                onClick={handleToggleMaximize}
                sx={{
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: 'primary.main',
                  border: `2px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                  width: 56,
                  height: 56,
                  transition: 'all 0.3s ease-in-out',
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.2),
                    transform: 'scale(1.05)',
                    boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.3)}`
                  },
                  '&:active': {
                    transform: 'scale(0.95)'
                  }
                }}
                aria-label={isMaximized ? 'Exit fullscreen mode' : 'Enter fullscreen mode'}
              >
                {isMaximized ? (
                  <FullscreenExit sx={{ fontSize: 28 }} />
                ) : (
                  <FullscreenIcon sx={{ fontSize: 28 }} />
                )}
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Admin Options Grid */}
        <Box sx={{ mb: 4 }}>
          {Object.entries(groupedOptions).map(([category, options]) => (
            <Box key={category} sx={{ mb: 4 }}>
              {/* Category Header */}
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 600,
                    color: categoryInfo[category as keyof typeof categoryInfo].color,
                    mb: 1,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                  }}
                >
                  <Box
                    sx={{
                      width: 4,
                      height: 24,
                      bgcolor: categoryInfo[category as keyof typeof categoryInfo].color,
                      borderRadius: 1
                    }}
                  />
                  {categoryInfo[category as keyof typeof categoryInfo].name}
                </Typography>
              </Box>

              {/* Options Grid */}
              <Grid container spacing={3}>
                {options.map((option) => (
                  <Grid
                    item
                    xs={12}
                    sm={6}
                    md={isMaximized ? 3 : 4}
                    lg={isMaximized ? 3 : 4}
                    xl={3}
                    key={option.id}
                  >
                    <Card
                      sx={{
                        height: '100%',
                        transition: 'all 0.3s ease-in-out',
                        cursor: 'pointer',
                        border: `2px solid transparent`,
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: `0 8px 32px ${alpha(option.color, 0.2)}`,
                          border: `2px solid ${alpha(option.color, 0.3)}`
                        },
                        '&:active': {
                          transform: 'translateY(-2px)'
                        }
                      }}
                    >
                      <CardActionArea
                        onClick={() => handleOptionClick(option)}
                        sx={{
                          height: '100%',
                          p: 0,
                          '&:focus-visible': {
                            outline: `3px solid ${alpha(option.color, 0.5)}`,
                            outlineOffset: '2px'
                          }
                        }}
                        aria-label={`Access ${option.title}: ${option.description}`}
                      >
                        <CardContent
                          sx={{
                            p: 3,
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            textAlign: 'center',
                            position: 'relative'
                          }}
                        >
                          {/* Icon */}
                          <Box
                            sx={{
                              width: 64,
                              height: 64,
                              borderRadius: '50%',
                              bgcolor: alpha(option.color, 0.1),
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              mb: 2,
                              transition: 'all 0.3s ease-in-out',
                              '.MuiCard-root:hover &': {
                                bgcolor: alpha(option.color, 0.2),
                                transform: 'scale(1.1)'
                              }
                            }}
                          >
                            {React.cloneElement(option.icon, {
                              sx: {
                                fontSize: 32,
                                color: option.color
                              }
                            })}
                          </Box>

                          {/* Title */}
                          <Typography
                            variant="h6"
                            component="h3"
                            sx={{
                              fontWeight: 600,
                              color: 'text.primary',
                              mb: 1,
                              lineHeight: 1.3
                            }}
                          >
                            {option.title}
                          </Typography>

                          {/* Description */}
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{
                              lineHeight: 1.5,
                              flexGrow: 1,
                              display: 'flex',
                              alignItems: 'center'
                            }}
                          >
                            {option.description}
                          </Typography>

                          {/* Category Chip */}
                          <Chip
                            label={categoryInfo[option.category].name}
                            size="small"
                            sx={{
                              mt: 2,
                              bgcolor: alpha(option.color, 0.1),
                              color: option.color,
                              fontSize: '0.7rem',
                              height: 24
                            }}
                          />
                        </CardContent>
                      </CardActionArea>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          ))}
        </Box>

        {/* Footer */}
        <Box
          sx={{
            mt: 6,
            pt: 3,
            borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            textAlign: 'center'
          }}
        >
          <Typography variant="body2" color="text.secondary">
            DataStatPro Admin Panel • {new Date().getFullYear()} • 
            {availableOptions.length} admin tools available
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default AdminMainPage;