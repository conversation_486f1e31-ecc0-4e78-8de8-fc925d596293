import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Box,
  Button,
  Chip,
  <PERSON>lap<PERSON>,
  IconButton,
  Link,
  Stack,
  Typography,
  useTheme,
  alpha,
  Fade,
  Paper
} from '@mui/material';
import {
  Close as CloseIcon,
  CardGiftcard as GiftIcon,
  YouTube as YouTubeIcon,
  Share as ShareIcon,
  Email as EmailIcon,
  Launch as LaunchIcon,
  Celebration as CelebrationIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface PromotionalNotificationProps {
  /** Whether to show the notification */
  show?: boolean;
  /** Callback when notification is dismissed */
  onDismiss?: () => void;
  /** Whether the notification can be dismissed */
  dismissible?: boolean;
  /** Position of the notification */
  position?: 'top' | 'bottom' | 'inline';
  /** Variant of the notification */
  variant?: 'banner' | 'card' | 'compact';
}

const PromotionalNotification: React.FC<PromotionalNotificationProps> = ({
  show = true,
  onDismiss,
  dismissible = true,
  position = 'top',
  variant = 'banner'
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [isVisible, setIsVisible] = useState(show);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    setIsVisible(show);
  }, [show]);

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss?.();
    // Store dismissal in localStorage to remember user preference
    localStorage.setItem('promotional-notification-dismissed', Date.now().toString());
  };

  const handleViewDetails = () => {
    navigate('/promotions');
  };

  const promotionalOffers = [
    {
      icon: <YouTubeIcon sx={{ color: '#FF0000' }} />,
      title: 'Subscribe to YouTube',
      reward: '1 Month Pro',
      description: 'Subscribe to our YouTube channel',
      action: 'Subscribe Now',
      link: 'https://www.youtube.com/@DataStatProLive'
    },
    {
      icon: <YouTubeIcon sx={{ color: '#FF0000' }} />,
      title: 'Create YouTube Video',
      reward: '6 Months Pro',
      description: 'Create a video about any DataStatPro feature',
      action: 'Learn More'
    },
    {
      icon: <ShareIcon sx={{ color: theme.palette.primary.main }} />,
      title: 'Social Media Post',
      reward: '1 Month Pro',
      description: 'Post about DataStatPro on social media',
      action: 'Learn More'
    }
  ];

  if (!isVisible) return null;

  const renderCompactVariant = () => (
    <Fade in={isVisible} timeout={500}>
      <Alert
        severity="info"
        icon={<GiftIcon />}
        action={
          <Stack direction="row" spacing={1} alignItems="center">
            <Button
              size="small"
              variant="contained"
              onClick={handleViewDetails}
              sx={{ minWidth: 'auto' }}
            >
              View Details
            </Button>
            {dismissible && (
              <IconButton
                size="small"
                onClick={handleDismiss}
                sx={{ color: 'inherit' }}
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            )}
          </Stack>
        }
        sx={{
          mb: 2,
          '& .MuiAlert-message': {
            width: '100%'
          }
        }}
      >
        <AlertTitle sx={{ mb: 1 }}>
          <Stack direction="row" alignItems="center" spacing={1}>
            <CelebrationIcon sx={{ fontSize: 20 }} />
            <Typography variant="subtitle2" component="span">
              Earn FREE Pro Months!
            </Typography>
          </Stack>
        </AlertTitle>
        <Typography variant="body2">
          Share DataStatPro and get free Pro subscription time. Multiple ways to earn!
        </Typography>
      </Alert>
    </Fade>
  );

  const renderCardVariant = () => (
    <Fade in={isVisible} timeout={500}>
      <Paper
        elevation={3}
        sx={{
          p: 3,
          mb: 3,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
          border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Background decoration */}
        <Box
          sx={{
            position: 'absolute',
            top: -20,
            right: -20,
            width: 100,
            height: 100,
            borderRadius: '50%',
            background: `linear-gradient(45deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.secondary.main, 0.1)})`,
            zIndex: 0
          }}
        />
        
        {dismissible && (
          <IconButton
            onClick={handleDismiss}
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              zIndex: 2
            }}
          >
            <CloseIcon />
          </IconButton>
        )}

        <Stack spacing={2} sx={{ position: 'relative', zIndex: 1 }}>
          <Stack direction="row" alignItems="center" spacing={2}>
            <GiftIcon sx={{ fontSize: 32, color: theme.palette.primary.main }} />
            <Box>
              <Typography variant="h6" component="h3" sx={{ fontWeight: 'bold' }}>
                🎁 Earn FREE DataStatPro Pro Months!
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Help spread the word and enjoy free Pro time
              </Typography>
            </Box>
          </Stack>

          <Stack spacing={1.5}>
            {promotionalOffers.map((offer, index) => (
              <Stack key={index} direction="row" alignItems="center" spacing={2}>
                <Box sx={{ minWidth: 24 }}>{offer.icon}</Box>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {offer.description}
                  </Typography>
                </Box>
                <Chip
                  label={offer.reward}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              </Stack>
            ))}
          </Stack>

          <Box sx={{ pt: 1 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              ✅ <strong>How to claim:</strong> Email proof (screenshot or link) from your registered email to{' '}
              <Link href="mailto:<EMAIL>" color="primary">
                <EMAIL>
              </Link>
            </Typography>
            
            <Stack direction="row" spacing={2} flexWrap="wrap">
              <Button
                variant="contained"
                startIcon={<LaunchIcon />}
                onClick={handleViewDetails}
                sx={{ minWidth: 140 }}
              >
                Full Details
              </Button>
              <Button
                variant="outlined"
                startIcon={<YouTubeIcon />}
                href="https://www.youtube.com/@DataStatProLive"
                target="_blank"
                rel="noopener noreferrer"
              >
                Subscribe Now
              </Button>
            </Stack>
          </Box>
        </Stack>
      </Paper>
    </Fade>
  );

  const renderBannerVariant = () => (
    <Fade in={isVisible} timeout={500}>
      <Box
        sx={{
          position: position === 'top' ? 'sticky' : 'relative',
          top: position === 'top' ? 0 : 'auto',
          bottom: position === 'bottom' ? 0 : 'auto',
          zIndex: position !== 'inline' ? 1200 : 'auto',
          width: '100%',
          background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
          color: 'white',
          py: 2,
          px: 3,
          boxShadow: theme.shadows[4]
        }}
      >
        <Stack
          direction={{ xs: 'column', md: 'row' }}
          alignItems={{ xs: 'flex-start', md: 'center' }}
          justifyContent="space-between"
          spacing={2}
        >
          <Stack direction="row" alignItems="center" spacing={2} sx={{ flex: 1 }}>
            <GiftIcon sx={{ fontSize: 28 }} />
            <Box>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                🎁 Earn FREE Pro Months by Sharing DataStatPro!
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                Subscribe to YouTube (1 month) • Create videos (6 months) • Social posts (1 month)
              </Typography>
            </Box>
          </Stack>

          <Stack direction="row" spacing={1} alignItems="center">
            <Button
              variant="contained"
              color="inherit"
              size="small"
              onClick={handleViewDetails}
              sx={{
                color: theme.palette.primary.main,
                bgcolor: 'white',
                '&:hover': {
                  bgcolor: alpha(theme.palette.common.white, 0.9)
                }
              }}
            >
              Learn More
            </Button>
            {dismissible && (
              <IconButton
                onClick={handleDismiss}
                sx={{ color: 'white' }}
                size="small"
              >
                <CloseIcon />
              </IconButton>
            )}
          </Stack>
        </Stack>
      </Box>
    </Fade>
  );

  switch (variant) {
    case 'compact':
      return renderCompactVariant();
    case 'card':
      return renderCardVariant();
    case 'banner':
    default:
      return renderBannerVariant();
  }
};

export default PromotionalNotification;