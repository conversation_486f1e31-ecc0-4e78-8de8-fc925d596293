import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Tabs,
  Tab,
  Alert,
  AlertTitle,
  CircularProgress,
  useTheme,
  alpha,
  IconButton,
  Tooltip,
  Zoom,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Chip,
  useMediaQuery,
  Divider,
  Badge
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  BarChart as BarChartIcon,
  Security as SecurityIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit,
  Minimize as MinimizeIcon,
  Maximize as MaximizeIcon,
  CardMembership as SubscriptionIcon,
  Storage as StorageIcon,
  MoreHoriz as MoreIcon,
  KeyboardArrowDown as ArrowDownIcon,
  Category as CategoryIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';

// Import admin dashboard components (to be created)
import AdminOverview from '../components/Admin/AdminOverview';
import UserManagement from '../components/Admin/UserManagement';
import SystemStatistics from '../components/Admin/SystemStatistics';
import NotificationManager from '../components/Admin/NotificationManager';
import AdminSettings from '../components/Admin/AdminSettings';
import SubscriptionOverrides from '../components/Admin/SubscriptionOverrides';
import DatasetQuotaManagement from '../components/Admin/DatasetQuotaManagement';
import AdminErrorBoundary from '../components/Admin/AdminErrorBoundary';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  const theme = useTheme();

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`admin-tabpanel-${index}`}
      aria-labelledby={`admin-tab-${index}`}
      aria-hidden={value !== index}
      tabIndex={value === index ? 0 : -1}
      {...other}
      style={{ 
        height: value === index ? 'auto' : 0, 
        overflow: value === index ? 'visible' : 'hidden',
        outline: 'none'
      }}
    >
      {value === index && (
        <Box 
          sx={{ 
            width: '100%', 
            height: '100%',
            '&:focus-visible': {
              outline: `2px solid ${theme.palette.primary.main}`,
              outlineOffset: '2px'
            }
          }}
        >
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `admin-tab-${index}`,
    'aria-controls': `admin-tabpanel-${index}`,
  };
}

const AdminDashboardPage: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { isAdmin, canAccessAdminDashboard, loading, user } = useAuth();
  const [activeTab, setActiveTab] = useState(0);
  const [pageLoading, setPageLoading] = useState(true);
  const [isMaximized, setIsMaximized] = useState(false);
  const [overflowMenuAnchor, setOverflowMenuAnchor] = useState<null | HTMLElement>(null);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  
  // Responsive breakpoints
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  const isLargeScreen = useMediaQuery(theme.breakpoints.up('lg'));

  // Check admin access on component mount
  useEffect(() => {
    if (!loading) {
      if (!user) {
        navigate('/auth');
        return;
      }

      if (!canAccessAdminDashboard) {
        navigate('/app/dashboard');
        return;
      }

      setPageLoading(false);
    }
  }, [loading, user, canAccessAdminDashboard, navigate]);

  // Function to handle maximize/minimize toggle
  const handleToggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  // Keyboard shortcut for maximize toggle (F11 or Ctrl/Cmd + Shift + F)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F11' ||
          ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'F')) {
        event.preventDefault();
        handleToggleMaximize();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isMaximized]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Handle window resize for responsive tab visibility
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle body overflow when maximized
  useEffect(() => {
    if (isMaximized) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isMaximized]);

  // Show loading while checking authentication
  if (loading || pageLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={48} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading Admin Dashboard...
          </Typography>
        </Box>
      </Container>
    );
  }

  // Show access denied if not admin
  if (!isAdmin || !canAccessAdminDashboard) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Access Denied
          </Typography>
          <Typography>
            You do not have permission to access the admin dashboard. Admin privileges are required.
          </Typography>
        </Alert>
      </Container>
    );
  }

  const tabs = [
    {
      label: 'Overview',
      shortLabel: 'Overview',
      icon: <DashboardIcon />,
      category: 'dashboard',
      priority: 1,
      component: (
        <AdminErrorBoundary>
          <AdminOverview />
        </AdminErrorBoundary>
      )
    },
    {
      label: 'User Management',
      shortLabel: 'Users',
      icon: <PeopleIcon />,
      category: 'management',
      priority: 2,
      component: (
        <AdminErrorBoundary>
          <UserManagement />
        </AdminErrorBoundary>
      )
    },
    {
      label: 'System Statistics',
      shortLabel: 'Stats',
      icon: <BarChartIcon />,
      category: 'analytics',
      priority: 3,
      component: (
        <AdminErrorBoundary>
          <SystemStatistics />
        </AdminErrorBoundary>
      )
    },
    {
      label: 'Subscription Overrides',
      shortLabel: 'Subscriptions',
      icon: <SubscriptionIcon />,
      category: 'management',
      priority: 4,
      component: (
        <AdminErrorBoundary>
          <SubscriptionOverrides />
        </AdminErrorBoundary>
      )
    },
    {
      label: 'Dataset & Storage Quotas',
      shortLabel: 'Storage',
      icon: <StorageIcon />,
      category: 'management',
      priority: 5,
      component: (
        <AdminErrorBoundary>
          <DatasetQuotaManagement />
        </AdminErrorBoundary>
      )
    },
    {
      label: 'Notifications',
      shortLabel: 'Notifications',
      icon: <NotificationsIcon />,
      category: 'communication',
      priority: 6,
      component: (
        <AdminErrorBoundary>
          <NotificationManager />
        </AdminErrorBoundary>
      )
    },
    {
      label: 'Admin Settings',
      shortLabel: 'Settings',
      icon: <SettingsIcon />,
      category: 'configuration',
      priority: 7,
      component: (
        <AdminErrorBoundary>
          <AdminSettings />
        </AdminErrorBoundary>
      )
    },
  ];

  // Calculate visible tabs based on screen size with width-based logic
  // FIXED: More conservative approach to ensure tabs are accessible at 100% zoom
  const getVisibleTabsCount = () => {
    if (isMaximized) return tabs.length;
    
    // Very narrow screens (mobile phones)
    if (windowWidth < 600) return 3;
    
    // Small tablets and large phones
    if (windowWidth < 768) return 4;
    
    // Medium tablets
    if (windowWidth < 900) return 5;
    
    // Large tablets and small desktops - more conservative for 100% zoom
    if (windowWidth < 1200) return 5;
    
    // Large desktops - be conservative to ensure accessibility at 100% zoom
    if (windowWidth < 1600) return 6;
    
    // Very large screens - show all tabs
    return tabs.length;
  };

  const visibleTabsCount = getVisibleTabsCount();
  const visibleTabs = tabs.slice(0, visibleTabsCount);
  const overflowTabs = tabs.slice(visibleTabsCount);
  const hasOverflow = overflowTabs.length > 0;

  // Handle overflow menu
  const handleOverflowMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setOverflowMenuAnchor(event.currentTarget);
  };

  const handleOverflowMenuClose = () => {
    setOverflowMenuAnchor(null);
  };

  const handleOverflowTabSelect = (tabIndex: number) => {
    setActiveTab(tabIndex);
    handleOverflowMenuClose();
  };

  // Enhanced keyboard navigation with accessibility improvements
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'ArrowLeft' && activeTab > 0) {
      event.preventDefault();
      setActiveTab(activeTab - 1);
      announceTabChange(activeTab - 1);
    } else if (event.key === 'ArrowRight' && activeTab < tabs.length - 1) {
      event.preventDefault();
      setActiveTab(activeTab + 1);
      announceTabChange(activeTab + 1);
    } else if (event.key === 'Home') {
      event.preventDefault();
      setActiveTab(0);
      announceTabChange(0);
    } else if (event.key === 'End') {
      event.preventDefault();
      setActiveTab(tabs.length - 1);
      announceTabChange(tabs.length - 1);
    }
  };

  // Screen reader announcements for tab changes
  const announceTabChange = (tabIndex: number) => {
    const announcement = `Switched to ${tabs[tabIndex].label} tab, ${tabIndex + 1} of ${tabs.length}`;
    const ariaLiveRegion = document.getElementById('tab-announcements');
    if (ariaLiveRegion) {
      ariaLiveRegion.textContent = announcement;
    }
  };

  // Focus management for overflow menu
  const handleOverflowMenuKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      handleOverflowMenuClose();
      // Return focus to the overflow button
      const overflowButton = document.querySelector('[aria-label="More tabs"]') as HTMLElement;
      if (overflowButton) {
        overflowButton.focus();
      }
    }
  };

  return (
    <Box
      sx={{
        width: '100%',
        minHeight: '100vh',
        bgcolor: 'background.default',
        position: isMaximized ? 'fixed' : 'relative',
        top: isMaximized ? 0 : 'auto',
        left: isMaximized ? 0 : 'auto',
        right: isMaximized ? 0 : 'auto',
        bottom: isMaximized ? 0 : 'auto',
        zIndex: isMaximized ? 1300 : 'auto', // Above sidebar and other content
        transition: 'all 0.3s ease-in-out',
        overflow: isMaximized ? 'auto' : 'visible'
      }}
    >
      {/* ARIA Live Region for Screen Reader Announcements */}
      <div
        id="tab-announcements"
        aria-live="polite"
        aria-atomic="true"
        style={{
          position: 'absolute',
          left: '-10000px',
          width: '1px',
          height: '1px',
          overflow: 'hidden'
        }}
      />

      <Container
        maxWidth={isMaximized ? false : "xl"}
        sx={{
          py: 3,
          px: { xs: 2, sm: 3 },
          maxWidth: isMaximized ? '100%' : undefined,
          width: isMaximized ? '100%' : undefined,
          transition: 'all 0.3s ease-in-out'
        }}
      >
        {/* Header */}
        <Box sx={{
          mb: isMaximized ? 2 : 3,
          transition: 'margin 0.3s ease-in-out'
        }}>
          <Typography variant="h3" component="h1" gutterBottom sx={{
            fontWeight: 'bold',
            background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            fontSize: {
              xs: isMaximized ? '1.5rem' : '1.8rem',
              sm: isMaximized ? '2rem' : '2.5rem'
            },
            transition: 'font-size 0.3s ease-in-out'
          }}>
            <SecurityIcon sx={{
              fontSize: {
                xs: isMaximized ? '1.5rem' : '2rem',
                sm: isMaximized ? '2rem' : '2.5rem'
              },
              color: theme.palette.primary.main,
              transition: 'font-size 0.3s ease-in-out'
            }} />
            Admin Dashboard
            {isMaximized && (
              <Box
                sx={{
                  ml: 2,
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                  fontSize: '0.75rem',
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  display: 'inline-flex',
                  alignItems: 'center',
                  fontWeight: 'bold',
                  animation: 'fadeIn 0.3s ease-in-out'
                }}
              >
                MAXIMIZED
              </Box>
            )}
          </Typography>
          {!isMaximized && (
            <Typography variant="h6" color="text.secondary" sx={{
              fontSize: { xs: '1rem', sm: '1.25rem' },
              transition: 'opacity 0.3s ease-in-out'
            }}>
              System administration and management console
            </Typography>
          )}
        </Box>

        {/* Admin Status Alert */}
        {!isMaximized && (
          <Alert severity="info" sx={{
            mb: 3,
            transition: 'opacity 0.3s ease-in-out'
          }}>
            <Typography variant="body2">
              <strong>Admin Access:</strong> You are logged in as an administrator.
              Please use these tools responsibly and follow your organization's policies.
            </Typography>
          </Alert>
        )}

        {/* Main Content */}
        <Paper
          elevation={0}
          variant="outlined"
          sx={{
            borderRadius: 2,
            overflow: 'hidden',
            backgroundColor: alpha(theme.palette.background.paper, isMaximized ? 0.95 : 0.8),
            backdropFilter: 'blur(10px)',
            minHeight: isMaximized ? 'calc(100vh - 120px)' : '70vh',
            transition: 'all 0.3s ease-in-out',
            boxShadow: isMaximized ? theme.shadows[8] : theme.shadows[1],
            '@keyframes fadeIn': {
              '0%': {
                opacity: 0,
                transform: 'translateY(-10px)'
              },
              '100%': {
                opacity: 1,
                transform: 'translateY(0)'
              }
            },
            '@keyframes pulse': {
              '0%': {
                transform: 'scale(1)',
                opacity: 1
              },
              '50%': {
                transform: 'scale(1.05)',
                opacity: 0.8
              },
              '100%': {
                transform: 'scale(1)',
                opacity: 1
              }
            }
          }}
        >
          {/* Enhanced Responsive Tabs Header with Maximize Button */}
          <Box sx={{
            borderBottom: 1,
            borderColor: 'divider',
            position: 'relative',
            display: 'flex',
            alignItems: 'center',
            minHeight: { xs: 64, sm: 72 }
          }}>
            <Box
              ref={tabsContainerRef}
              sx={{
                flexGrow: 1,
                display: 'flex',
                alignItems: 'center',
                overflow: 'hidden'
              }}
              onKeyDown={handleKeyDown}
            >
              {/* Visible Tabs */}
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                flexGrow: 1,
                overflow: hasOverflow ? 'hidden' : 'auto',
                // Enable horizontal scrolling when no overflow menu is shown
                overflowX: hasOverflow ? 'hidden' : 'auto',
                scrollbarWidth: 'thin',
                '&::-webkit-scrollbar': {
                  height: 4,
                },
                '&::-webkit-scrollbar-track': {
                  backgroundColor: 'transparent',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.3),
                  borderRadius: 2,
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.5),
                  }
                }
              }}>
                {visibleTabs.map((tab, index) => {
                  const isActive = activeTab === index;
                  const isInOverflow = activeTab >= visibleTabsCount;

                  return (
                    <Box
                      key={index}
                      onClick={() => setActiveTab(index)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          setActiveTab(index);
                        }
                      }}
                      tabIndex={0}
                      role="tab"
                      aria-selected={isActive}
                      aria-controls={`admin-tabpanel-${index}`}
                      id={`admin-tab-${index}`}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        px: { xs: 1.5, sm: 2.5 },
                        py: { xs: 1.5, sm: 2 },
                        minHeight: { xs: 60, sm: 72 },
                        cursor: 'pointer',
                        position: 'relative',
                        transition: 'all 0.2s ease-in-out',
                        borderRadius: '8px 8px 0 0',
                        backgroundColor: isActive
                          ? alpha(theme.palette.primary.main, 0.12)
                          : 'transparent',
                        color: isActive
                          ? theme.palette.primary.main
                          : theme.palette.text.secondary,
                        fontWeight: isActive ? 700 : 500,
                        fontSize: { xs: '0.875rem', sm: '1rem' },
                        textTransform: 'none',
                        boxShadow: isActive
                          ? `inset 0 -3px 0 ${theme.palette.primary.main}, 0 2px 8px ${alpha(theme.palette.primary.main, 0.15)}`
                          : 'none',
                        '&:hover': {
                          backgroundColor: isActive
                            ? alpha(theme.palette.primary.main, 0.16)
                            : alpha(theme.palette.action.hover, 0.12),
                          color: isActive
                            ? theme.palette.primary.main
                            : theme.palette.text.primary,
                          transform: 'translateY(-2px)',
                          boxShadow: isActive
                            ? `inset 0 -3px 0 ${theme.palette.primary.main}, 0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`
                            : `0 2px 8px ${alpha(theme.palette.action.hover, 0.1)}`
                        },
                        '&:focus-visible': {
                          outline: `3px solid ${alpha(theme.palette.primary.main, 0.5)}`,
                          outlineOffset: '2px',
                          backgroundColor: alpha(theme.palette.primary.main, 0.08)
                        },
                        '&::before': isActive ? {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: '50%',
                          transform: 'translateX(-50%)',
                          width: '60%',
                          height: 2,
                          backgroundColor: theme.palette.primary.main,
                          borderRadius: '0 0 2px 2px',
                          animation: 'slideIn 0.3s ease-out'
                        } : {},
                        '&::after': isActive ? {
                          content: '""',
                          position: 'absolute',
                          bottom: 0,
                          left: 0,
                          right: 0,
                          height: 4,
                          background: `linear-gradient(90deg, ${alpha(theme.palette.primary.main, 0.8)}, ${theme.palette.primary.main}, ${alpha(theme.palette.primary.main, 0.8)})`,
                          borderRadius: '4px 4px 0 0',
                          animation: 'expandWidth 0.3s ease-out'
                        } : {},
                        '@keyframes slideIn': {
                          '0%': { width: '0%', opacity: 0 },
                          '100%': { width: '60%', opacity: 1 }
                        },
                        '@keyframes expandWidth': {
                          '0%': { transform: 'scaleX(0)' },
                          '100%': { transform: 'scaleX(1)' }
                        }
                      }}
                    >
                      <Box sx={{
                        color: 'inherit',
                        display: 'flex',
                        alignItems: 'center',
                        transform: isActive ? 'scale(1.1)' : 'scale(1)',
                        transition: 'transform 0.2s ease-in-out',
                        filter: isActive ? 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' : 'none'
                      }}>
                        {tab.icon}
                      </Box>
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 'inherit',
                          fontSize: 'inherit',
                          display: { xs: 'none', sm: 'block' },
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {isMobile ? tab.shortLabel : tab.label}
                      </Typography>
                      {/* Mobile label */}
                      {isMobile && (
                        <Typography
                          variant="caption"
                          sx={{
                            fontWeight: 'inherit',
                            fontSize: '0.75rem',
                            display: { xs: 'block', sm: 'none' },
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {tab.shortLabel}
                        </Typography>
                      )}
                    </Box>
                  );
                })}
              </Box>

              {/* Overflow Menu Button */}
              {hasOverflow && (
                <Box
                  onClick={handleOverflowMenuOpen}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleOverflowMenuOpen(e as any);
                    }
                  }}
                  tabIndex={0}
                  role="button"
                  aria-label="More tabs"
                  aria-expanded={Boolean(overflowMenuAnchor)}
                  aria-haspopup="true"
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5,
                    px: { xs: 1, sm: 1.5 },
                    py: { xs: 1.5, sm: 2 },
                    minHeight: { xs: 60, sm: 72 },
                    cursor: 'pointer',
                    position: 'relative',
                    transition: 'all 0.2s ease-in-out',
                    borderRadius: '8px 8px 0 0',
                    backgroundColor: activeTab >= visibleTabsCount
                      ? alpha(theme.palette.primary.main, 0.12)
                      : 'transparent',
                    color: activeTab >= visibleTabsCount
                      ? theme.palette.primary.main
                      : theme.palette.text.secondary,
                    fontWeight: activeTab >= visibleTabsCount ? 700 : 500,
                    boxShadow: activeTab >= visibleTabsCount
                      ? `inset 0 -3px 0 ${theme.palette.primary.main}, 0 2px 8px ${alpha(theme.palette.primary.main, 0.15)}`
                      : 'none',
                    '&:hover': {
                      backgroundColor: activeTab >= visibleTabsCount
                        ? alpha(theme.palette.primary.main, 0.16)
                        : alpha(theme.palette.action.hover, 0.12),
                      color: activeTab >= visibleTabsCount
                        ? theme.palette.primary.main
                        : theme.palette.text.primary,
                      transform: 'translateY(-2px)',
                      boxShadow: activeTab >= visibleTabsCount
                        ? `inset 0 -3px 0 ${theme.palette.primary.main}, 0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`
                        : `0 2px 8px ${alpha(theme.palette.action.hover, 0.1)}`
                    },
                    '&:focus-visible': {
                      outline: `3px solid ${alpha(theme.palette.primary.main, 0.5)}`,
                      outlineOffset: '2px',
                      backgroundColor: alpha(theme.palette.primary.main, 0.08)
                    },
                    '&::before': activeTab >= visibleTabsCount ? {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      width: '60%',
                      height: 2,
                      backgroundColor: theme.palette.primary.main,
                      borderRadius: '0 0 2px 2px',
                      animation: 'slideIn 0.3s ease-out'
                    } : {},
                    '&::after': activeTab >= visibleTabsCount ? {
                      content: '""',
                      position: 'absolute',
                      bottom: 0,
                      left: 0,
                      right: 0,
                      height: 4,
                      background: `linear-gradient(90deg, ${alpha(theme.palette.primary.main, 0.8)}, ${theme.palette.primary.main}, ${alpha(theme.palette.primary.main, 0.8)})`,
                      borderRadius: '4px 4px 0 0',
                      animation: 'expandWidth 0.3s ease-out'
                    } : {}
                  }}
                >
                  <MoreIcon sx={{
                    fontSize: '1.25rem',
                    transform: activeTab >= visibleTabsCount ? 'scale(1.1)' : 'scale(1)',
                    transition: 'transform 0.2s ease-in-out',
                    filter: activeTab >= visibleTabsCount ? 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' : 'none'
                  }} />
                  {activeTab >= visibleTabsCount && (
                    <Badge
                      color="primary"
                      variant="dot"
                      sx={{
                        '& .MuiBadge-dot': {
                          right: -2,
                          top: 2
                        }
                      }}
                    />
                  )}
                  <ArrowDownIcon sx={{ fontSize: '1rem', ml: 0.5 }} />
                </Box>
              )}

              {/* Overflow Menu */}
              <Menu
                anchorEl={overflowMenuAnchor}
                open={Boolean(overflowMenuAnchor)}
                onClose={handleOverflowMenuClose}
                onKeyDown={handleOverflowMenuKeyDown}
                MenuListProps={{
                  'aria-label': 'Additional admin tabs',
                  role: 'menu'
                }}
                PaperProps={{
                  sx: {
                    mt: 1,
                    minWidth: 200,
                    maxWidth: 280,
                    borderRadius: 2,
                    boxShadow: theme.shadows[8],
                    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                  }
                }}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
              >
                {overflowTabs.map((tab, index) => {
                const tabIndex = visibleTabsCount + index;
                const isActive = activeTab === tabIndex;

                return (
                  <MenuItem
                    key={tabIndex}
                    onClick={() => handleOverflowTabSelect(tabIndex)}
                    selected={isActive}
                    role="menuitem"
                    aria-label={`Switch to ${tab.label} tab`}
                    aria-current={isActive ? 'page' : undefined}
                      sx={{
                        py: 1.5,
                        px: 2,
                        borderRadius: 1,
                        mx: 0.5,
                        mb: index === overflowTabs.length - 1 ? 0.5 : 0,
                        backgroundColor: isActive
                          ? alpha(theme.palette.primary.main, 0.12)
                          : 'transparent',
                        borderLeft: isActive ? `4px solid ${theme.palette.primary.main}` : '4px solid transparent',
                        boxShadow: isActive ? `0 2px 8px ${alpha(theme.palette.primary.main, 0.15)}` : 'none',
                        transform: isActive ? 'translateX(4px)' : 'translateX(0)',
                        transition: 'all 0.2s ease-in-out',
                        '&:hover': {
                          backgroundColor: isActive
                            ? alpha(theme.palette.primary.main, 0.16)
                            : alpha(theme.palette.action.hover, 0.12),
                          transform: 'translateX(6px)',
                          boxShadow: isActive
                            ? `0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`
                            : `0 2px 8px ${alpha(theme.palette.action.hover, 0.1)}`
                        }
                      }}
                    >
                      <ListItemIcon sx={{
                        color: isActive ? theme.palette.primary.main : 'inherit',
                        minWidth: 36,
                        transform: isActive ? 'scale(1.1)' : 'scale(1)',
                        transition: 'transform 0.2s ease-in-out',
                        filter: isActive ? 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' : 'none'
                      }}>
                        {tab.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={tab.label}
                        sx={{
                          '& .MuiListItemText-primary': {
                            fontWeight: isActive ? 600 : 400,
                            color: isActive ? theme.palette.primary.main : 'inherit'
                          }
                        }}
                      />
                      {isActive && (
                        <Chip
                          size="small"
                          label="Active"
                          color="primary"
                          variant="filled"
                          sx={{
                            ml: 1,
                            height: 22,
                            fontSize: '0.7rem',
                            fontWeight: 600,
                            boxShadow: `0 2px 4px ${alpha(theme.palette.primary.main, 0.3)}`,
                            animation: 'pulse 2s infinite'
                          }}
                        />
                      )}
                    </MenuItem>
                  );
                })}
              </Menu>
            </Box>

            {/* Enhanced Maximize/Minimize Button */}
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              pl: 2
            }}>
              <Divider orientation="vertical" flexItem sx={{ height: 32 }} />
              <Tooltip
                title={isMaximized ? 'Exit Fullscreen' : 'Enter Fullscreen'}
                placement="bottom"
              >
                <IconButton
                    onClick={handleToggleMaximize}
                    size={isMobile ? 'small' : 'medium'}
                  sx={{
                    backgroundColor: alpha(theme.palette.background.paper, 0.9),
                    backdropFilter: 'blur(4px)',
                    border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                    borderRadius: 2,
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.primary.main, 0.1),
                      borderColor: alpha(theme.palette.primary.main, 0.3),
                      transform: 'scale(1.05)',
                    },
                    '&:focus-visible': {
                      outline: `2px solid ${theme.palette.primary.main}`,
                      outlineOffset: 2
                    },
                    transition: 'all 0.2s ease-in-out',
                  }}
                  aria-label={isMaximized ? 'Exit Fullscreen' : 'Enter Fullscreen'}
                >
                  {isMaximized ? <FullscreenExit /> : <FullscreenIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Tab Panels */}
          <Box sx={{
            overflow: 'auto',
            maxHeight: isMaximized ? 'calc(100vh - 200px)' : 'calc(100vh - 300px)',
            transition: 'max-height 0.3s ease-in-out'
          }}>
            {tabs.map((tab, index) => (
              <TabPanel key={index} value={activeTab} index={index}>
                <Box sx={{
                  px: { xs: 1, sm: isMaximized ? 3 : 2 },
                  py: 2,
                  transition: 'padding 0.3s ease-in-out'
                }}>
                  {tab.component}
                </Box>
              </TabPanel>
            ))}
          </Box>
        </Paper>

        {/* Footer */}
        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary" sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
            DataStatPro Admin Dashboard - Use responsibly and in accordance with your organization's policies
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default AdminDashboardPage;
