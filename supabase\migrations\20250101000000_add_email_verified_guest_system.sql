-- Email-Verified Guest Access System Migration
-- This migration adds support for email-verified guest users with tracking capabilities

-- Create guest_users table with comprehensive tracking
CREATE TABLE IF NOT EXISTS public.guest_users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    verification_expires_at TIMESTAMP WITH TIME ZONE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    total_sessions INTEGER DEFAULT 0,
    total_session_duration INTEGER DEFAULT 0, -- in seconds
    preferences JSONB DEFAULT '{}',
    marketing_consent BOOLEAN DEFAULT FALSE,
    privacy_consent BOOLEAN DEFAULT TRUE,
    data_retention_period VARCHAR(20) DEFAULT '90days',
    migrated_from_anonymous BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_retention_period CHECK (data_retention_period IN ('30days', '90days', '1year', '2years'))
);

-- Create guest_analytics table for detailed usage tracking
CREATE TABLE IF NOT EXISTS public.guest_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    guest_user_id UUID REFERENCES public.guest_users(id) ON DELETE CASCADE,
    session_id VARCHAR(100) NOT NULL,
    session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    session_end TIMESTAMP WITH TIME ZONE,
    session_duration INTEGER, -- calculated duration in seconds
    features_used JSONB DEFAULT '[]',
    pages_visited JSONB DEFAULT '[]',
    analysis_methods_used JSONB DEFAULT '[]',
    ip_address INET, -- anonymized IP for analytics
    user_agent TEXT,
    referrer_url TEXT,
    device_type VARCHAR(50), -- mobile, tablet, desktop
    browser_name VARCHAR(50),
    os_name VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create table to track guest migration from anonymous to verified
CREATE TABLE IF NOT EXISTS public.guest_migration_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id VARCHAR(100),
    migration_type VARCHAR(50) CHECK (migration_type IN ('prompted', 'completed', 'skipped', 'abandoned')),
    source_type VARCHAR(50) DEFAULT 'anonymous_guest',
    target_type VARCHAR(50) DEFAULT 'verified_guest',
    guest_user_id UUID REFERENCES public.guest_users(id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Note: statistical_method_analytics and analysis_workflows tables don't exist yet
-- These columns will be added when those tables are created in future migrations

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_guest_users_email ON public.guest_users(email);
CREATE INDEX IF NOT EXISTS idx_guest_users_verification_token ON public.guest_users(verification_token);
CREATE INDEX IF NOT EXISTS idx_guest_users_created_at ON public.guest_users(created_at);
CREATE INDEX IF NOT EXISTS idx_guest_analytics_guest_user_id ON public.guest_analytics(guest_user_id);
CREATE INDEX IF NOT EXISTS idx_guest_analytics_session_start ON public.guest_analytics(session_start);

-- Row Level Security (RLS) policies
ALTER TABLE public.guest_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.guest_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.guest_migration_log ENABLE ROW LEVEL SECURITY;

-- Policies for guest_users table
CREATE POLICY "Guest users can view their own data" ON public.guest_users
    FOR SELECT USING (true); -- Allow system-level access for verification

CREATE POLICY "System can manage guest users" ON public.guest_users
    FOR ALL USING (true); -- Full system access for backend functions

-- Policies for guest_analytics table
CREATE POLICY "System can manage guest analytics" ON public.guest_analytics
    FOR ALL USING (true); -- System-level access for analytics

-- Policies for guest_migration_log table
CREATE POLICY "System can manage migration log" ON public.guest_migration_log
    FOR ALL USING (true); -- System-level access for migration tracking

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.guest_users TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.guest_analytics TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.guest_migration_log TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Grant permissions to anon role for guest access
GRANT SELECT ON public.guest_users TO anon;
GRANT INSERT ON public.guest_users TO anon;
GRANT UPDATE ON public.guest_users TO anon;

-- Function to create guest user with validation
CREATE OR REPLACE FUNCTION public.create_guest_user(
    p_email VARCHAR(255),
    p_full_name VARCHAR(255) DEFAULT NULL,
    p_institution VARCHAR(255) DEFAULT NULL,
    p_marketing_consent BOOLEAN DEFAULT FALSE
) RETURNS TABLE(
    guest_id UUID,
    verification_token VARCHAR(255),
    success BOOLEAN,
    message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_guest_id UUID;
    v_token VARCHAR(255);
    v_existing_user UUID;
BEGIN
    -- Check if email already exists
    SELECT id INTO v_existing_user 
    FROM public.guest_users 
    WHERE email = p_email;
    
    IF v_existing_user IS NOT NULL THEN
        RETURN QUERY SELECT 
            v_existing_user,
            NULL::VARCHAR(255),
            FALSE,
            'Email already registered'::TEXT;
        RETURN;
    END IF;
    
    -- Generate verification token
    v_token := encode(gen_random_bytes(32), 'hex');
    
    -- Insert guest user
    INSERT INTO public.guest_users (
        email, 
        verification_token, 
        verification_expires_at,
        marketing_consent
    ) VALUES (
        p_email, 
        v_token, 
        NOW() + INTERVAL '24 hours',
        p_marketing_consent
    ) RETURNING id INTO v_guest_id;
    
    RETURN QUERY SELECT 
        v_guest_id,
        v_token,
        TRUE,
        'Guest user created successfully'::TEXT;
        
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT 
            NULL::UUID,
            NULL::VARCHAR(255),
            FALSE,
            SQLERRM::TEXT;
END;
$$;

-- Function to verify guest email
CREATE OR REPLACE FUNCTION public.verify_guest_email(
    p_token VARCHAR(255)
) RETURNS TABLE(
    guest_id UUID,
    email VARCHAR(255),
    success BOOLEAN,
    message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_guest_record RECORD;
BEGIN
    -- Find and verify the token
    UPDATE public.guest_users 
    SET 
        email_verified = TRUE,
        verification_token = NULL,
        verification_expires_at = NULL,
        updated_at = NOW()
    WHERE 
        verification_token = p_token 
        AND verification_expires_at > NOW()
        AND email_verified = FALSE
    RETURNING id, email INTO v_guest_record;
    
    IF v_guest_record.id IS NOT NULL THEN
        RETURN QUERY SELECT 
            v_guest_record.id,
            v_guest_record.email,
            TRUE,
            'Email verified successfully'::TEXT;
    ELSE
        RETURN QUERY SELECT 
            NULL::UUID,
            NULL::VARCHAR(255),
            FALSE,
            'Invalid or expired verification token'::TEXT;
    END IF;
END;
$$;

-- Function to get guest user by email
CREATE OR REPLACE FUNCTION public.get_guest_user_by_email(
    p_email VARCHAR(255)
) RETURNS TABLE(
    id UUID,
    email VARCHAR(255),
    email_verified BOOLEAN,
    last_login_at TIMESTAMP WITH TIME ZONE,
    total_sessions INTEGER,
    preferences JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        gu.id,
        gu.email,
        gu.email_verified,
        gu.last_login_at,
        gu.total_sessions,
        gu.preferences
    FROM public.guest_users gu
    WHERE gu.email = p_email AND gu.email_verified = TRUE;
END;
$$;

-- Function to update guest login
CREATE OR REPLACE FUNCTION public.update_guest_login(
    p_guest_id UUID
) RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.guest_users 
    SET 
        last_login_at = NOW(),
        total_sessions = total_sessions + 1,
        updated_at = NOW()
    WHERE id = p_guest_id;
END;
$$;

-- Function to track guest analytics session
CREATE OR REPLACE FUNCTION public.start_guest_analytics_session(
    p_guest_user_id UUID,
    p_session_id VARCHAR(100),
    p_user_agent TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_analytics_id UUID;
BEGIN
    INSERT INTO public.guest_analytics (
        guest_user_id,
        session_id,
        user_agent,
        ip_address
    ) VALUES (
        p_guest_user_id,
        p_session_id,
        p_user_agent,
        p_ip_address
    ) RETURNING id INTO v_analytics_id;
    
    RETURN v_analytics_id;
END;
$$;

-- Function to end guest analytics session
CREATE OR REPLACE FUNCTION public.end_guest_analytics_session(
    p_session_id VARCHAR(100),
    p_features_used JSONB DEFAULT '[]',
    p_pages_visited JSONB DEFAULT '[]'
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.guest_analytics 
    SET 
        session_end = NOW(),
        session_duration = EXTRACT(EPOCH FROM (NOW() - session_start))::INTEGER,
        features_used = p_features_used,
        pages_visited = p_pages_visited
    WHERE session_id = p_session_id AND session_end IS NULL;
    
    RETURN FOUND;
END;
$$;

-- Function for automatic cleanup of old guest data
CREATE OR REPLACE FUNCTION public.cleanup_old_guest_data()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Delete unverified guest users after 7 days
    DELETE FROM public.guest_users 
    WHERE email_verified = FALSE 
        AND created_at < NOW() - INTERVAL '7 days';
    
    -- Delete analytics data older than 2 years
    DELETE FROM public.guest_analytics 
    WHERE created_at < NOW() - INTERVAL '2 years';
    
    -- Delete inactive verified guests after 1 year of no activity
    DELETE FROM public.guest_users 
    WHERE email_verified = TRUE 
        AND (last_login_at IS NULL OR last_login_at < NOW() - INTERVAL '1 year')
        AND created_at < NOW() - INTERVAL '1 year';
END;
$$;

-- Comments for documentation
COMMENT ON TABLE public.guest_users IS 'Stores email-verified guest users for tracking and analytics';
COMMENT ON TABLE public.guest_analytics IS 'Tracks usage analytics for verified guest users';
COMMENT ON TABLE public.guest_migration_log IS 'Tracks migration from anonymous to verified guest users';
COMMENT ON COLUMN public.guest_users.marketing_consent IS 'User consent for marketing communications';
COMMENT ON COLUMN public.guest_users.data_retention_period IS 'User-selected data retention period for GDPR compliance';
COMMENT ON COLUMN public.guest_analytics.session_duration IS 'Calculated session duration in seconds';
COMMENT ON COLUMN public.guest_analytics.ip_address IS 'Anonymized IP address for analytics purposes';

-- Initial sample data for testing (optional)
INSERT INTO public.guest_users (email, email_verified, preferences, marketing_consent) VALUES
('<EMAIL>', true, '{"theme": "light", "language": "en"}', false),
('<EMAIL>', true, '{"theme": "dark", "language": "en", "analyticsConsent": true}', true)
ON CONFLICT (email) DO NOTHING;

-- Sample analytics data
INSERT INTO public.guest_analytics (
    guest_user_id, 
    session_id, 
    session_start, 
    session_end, 
    session_duration,
    features_used, 
    pages_visited,
    device_type,
    browser_name
) 
SELECT 
    gu.id,
    'session_' || gen_random_uuid()::text,
    NOW() - INTERVAL '2 hours',
    NOW() - INTERVAL '1 hour',
    3600,
    '["data_upload", "statistical_analysis", "chart_generation"]'::jsonb,
    '["/dashboard", "/analysis", "/charts"]'::jsonb,
    'desktop',
    'Chrome'
FROM public.guest_users gu 
WHERE gu.email_verified = true
LIMIT 2
ON CONFLICT DO NOTHING;