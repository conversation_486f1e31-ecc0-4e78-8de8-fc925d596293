// Admin routes configuration

import { lazy } from 'react';
import { EnhancedRouteConfig } from '../../types/routing';

const AdminMainPage = lazy(() => import('../../pages/AdminMainPage'));
const AdminDashboardPage = lazy(() => import('../../pages/AdminDashboardPage'));
const UserManagementPage = lazy(() => import('../../pages/admin/UserManagementPage'));
const DatasetQuotaPage = lazy(() => import('../../pages/admin/DatasetQuotaPage'));
const AdminOverviewPage = lazy(() => import('../../pages/admin/AdminOverviewPage'));
const AdminSettingsPage = lazy(() => import('../../pages/admin/AdminSettingsPage'));
const SystemMonitoringPage = lazy(() => import('../../pages/admin/SystemMonitoringPage'));
const AnalyticsDashboardPage = lazy(() => import('../../pages/admin/AnalyticsDashboardPage'));
const SecuritySettingsPage = lazy(() => import('../../pages/admin/SecuritySettingsPage'));
const BackupRecoveryPage = lazy(() => import('../../pages/admin/BackupRecoveryPage'));
const APIManagementPage = lazy(() => import('../../pages/admin/APIManagementPage'));
const AuditLogsPage = lazy(() => import('../../pages/admin/AuditLogsPage'));

export const adminRoutes: EnhancedRouteConfig[] = [
  {
    path: 'admin',
    component: AdminMainPage,
    requiresAuth: true,
    requiresAdmin: true,
    allowGuest: false,
    allowPublic: false,
    props: {},
    metadata: {
      title: 'Admin Dashboard',
      description: 'Main administration dashboard with navigation options',
      category: 'admin',
      icon: 'Security',
      order: 999,
      hidden: false,
      adminOnly: true
    }
  },
  {
    path: 'admin-dashboard',
    component: AdminMainPage,
    requiresAuth: true,
    requiresAdmin: true,
    allowGuest: false,
    allowPublic: false,
    props: {},
    metadata: {
      title: 'Admin Dashboard',
      description: 'Main administration dashboard with navigation options',
      category: 'admin',
      icon: 'Security',
      order: 998,
      hidden: false,
      adminOnly: true
    }
  },
  {
    path: 'admin/overview',
    component: AdminDashboardPage,
    requiresAuth: true,
    requiresAdmin: true,
    allowGuest: false,
    allowPublic: false,
    props: {},
    metadata: {
      title: 'Admin Overview',
      description: 'System overview and key metrics',
      category: 'admin',
      icon: 'Dashboard',
      order: 1000,
      hidden: true,
      adminOnly: true
    }
  },
  {
    path: 'admin/users',
    component: UserManagementPage,
    requiresAuth: true,
    requiresAdmin: true,
    allowGuest: false,
    allowPublic: false,
    props: {},
    metadata: {
      title: 'User Management',
      description: 'Manage user accounts and permissions',
      category: 'admin',
      icon: 'People',
      order: 1001,
      hidden: true,
      adminOnly: true
    }
  },
  {
    path: 'admin/dataset-quota',
    component: DatasetQuotaPage,
    requiresAuth: true,
    requiresAdmin: true,
    allowGuest: false,
    allowPublic: false,
    props: {},
    metadata: {
      title: 'Dataset Quota Management',
      description: 'Manage dataset quotas and limits',
      category: 'admin',
      icon: 'Storage',
      order: 1002,
      hidden: true,
      adminOnly: true
    }
  },
  {
    path: 'admin/settings',
    component: AdminSettingsPage,
    requiresAuth: true,
    requiresAdmin: true,
    allowGuest: false,
    allowPublic: false,
    props: {},
    metadata: {
      title: 'Admin Settings',
      description: 'Configure system settings and preferences',
      category: 'admin',
      icon: 'Settings',
      order: 1003,
      hidden: true,
      adminOnly: true
    }
  },
  {
    path: 'admin/monitoring',
    component: SystemMonitoringPage,
    requiresAuth: true,
    requiresAdmin: true,
    allowGuest: false,
    allowPublic: false,
    props: {},
    metadata: {
      title: 'System Monitoring',
      description: 'Monitor system performance and health',
      category: 'admin',
      icon: 'Monitoring',
      order: 1004,
      hidden: true,
      adminOnly: true
    }
  },
  {
    path: 'admin/analytics',
    component: AnalyticsDashboardPage,
    requiresAuth: true,
    requiresAdmin: true,
    allowGuest: false,
    allowPublic: false,
    props: {},
    metadata: {
      title: 'Analytics Dashboard',
      description: 'View detailed analytics and reports',
      category: 'admin',
      icon: 'Analytics',
      order: 1005,
      hidden: true,
      adminOnly: true
    }
  },
  {
    path: 'admin/security',
    component: SecuritySettingsPage,
    requiresAuth: true,
    requiresAdmin: true,
    allowGuest: false,
    allowPublic: false,
    props: {},
    metadata: {
      title: 'Security Settings',
      description: 'Configure security policies and access controls',
      category: 'admin',
      icon: 'Security',
      order: 1006,
      hidden: true,
      adminOnly: true
    }
  },
  {
    path: 'admin/backup',
    component: BackupRecoveryPage,
    requiresAuth: true,
    requiresAdmin: true,
    allowGuest: false,
    allowPublic: false,
    props: {},
    metadata: {
      title: 'Backup & Recovery',
      description: 'Manage system backups and recovery options',
      category: 'admin',
      icon: 'Backup',
      order: 1007,
      hidden: true,
      adminOnly: true
    }
  },
  {
    path: 'admin/api',
    component: APIManagementPage,
    requiresAuth: true,
    requiresAdmin: true,
    allowGuest: false,
    allowPublic: false,
    props: {},
    metadata: {
      title: 'API Management',
      description: 'Manage API keys and access controls',
      category: 'admin',
      icon: 'Api',
      order: 1008,
      hidden: true,
      adminOnly: true
    }
  },
  {
    path: 'admin/audit',
    component: AuditLogsPage,
    requiresAuth: true,
    requiresAdmin: true,
    allowGuest: false,
    allowPublic: false,
    props: {},
    metadata: {
      title: 'Audit Logs',
      description: 'View system audit logs and user activities',
      category: 'admin',
      icon: 'History',
      order: 1009,
      hidden: true,
      adminOnly: true
    }
  }
];

// Export admin route paths for easy reference
export const ADMIN_ROUTES = {
  MAIN: 'admin',
  OVERVIEW: 'admin/overview',
  USERS: 'admin/users',
  DATASET_QUOTA: 'admin/dataset-quota',
  SETTINGS: 'admin/settings',
  MONITORING: 'admin/monitoring',
  ANALYTICS: 'admin/analytics',
  SECURITY: 'admin/security',
  BACKUP: 'admin/backup',
  API: 'admin/api',
  AUDIT: 'admin/audit'
} as const;

// Utility function to check if a route is admin-only
export const isAdminRoute = (path: string): boolean => {
  return path.startsWith('admin-') || path.includes('admin');
};

// Utility function to get admin routes for navigation
export const getAdminNavigationRoutes = (): EnhancedRouteConfig[] => {
  return adminRoutes.filter(route => !route.metadata?.hidden);
};
