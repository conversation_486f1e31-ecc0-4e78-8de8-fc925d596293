import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  IconButton,
  Tooltip,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  AlertTitle,
  ListItemIcon,
  Avatar,
  Breadcrumbs,
  Link
} from '@mui/material';
import {
  ArrowBack,
  FullscreenExit,
  Fullscreen as FullscreenIcon,
  Backup,
  Restore,
  CloudDownload,
  CloudUpload,
  Schedule,
  Storage,
  CheckCircle,
  Error,
  Warning,
  PlayArrow,
  Stop,
  Download,
  Delete,
  Settings,
  History,
  Folder,
  Storage as Database,
  Security,
  Refresh
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import PageTitle from '../../components/UI/PageTitle';

interface BackupJob {
  id: string;
  name: string;
  type: 'full' | 'incremental' | 'differential';
  status: 'completed' | 'running' | 'failed' | 'scheduled';
  lastRun: string;
  nextRun: string;
  size: string;
  duration: string;
  schedule: string;
}

interface BackupHistory {
  id: string;
  name: string;
  type: string;
  timestamp: string;
  size: string;
  status: 'success' | 'failed' | 'partial';
  location: string;
}

interface StorageLocation {
  id: string;
  name: string;
  type: 'local' | 'cloud' | 'network';
  path: string;
  available: string;
  used: string;
  status: 'online' | 'offline' | 'error';
}

const BackupRecoveryPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const [isMaximized, setIsMaximized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState<'backup' | 'restore' | 'schedule'>('backup');
  const [backupProgress, setBackupProgress] = useState(0);
  const [isBackupRunning, setIsBackupRunning] = useState(false);

  const [backupJobs] = useState<BackupJob[]>([
    {
      id: '1',
      name: 'Daily Database Backup',
      type: 'incremental',
      status: 'completed',
      lastRun: '2024-01-15 02:00:00',
      nextRun: '2024-01-16 02:00:00',
      size: '2.3 GB',
      duration: '15 minutes',
      schedule: 'Daily at 2:00 AM'
    },
    {
      id: '2',
      name: 'Weekly Full System Backup',
      type: 'full',
      status: 'scheduled',
      lastRun: '2024-01-08 01:00:00',
      nextRun: '2024-01-15 01:00:00',
      size: '45.7 GB',
      duration: '2 hours 30 minutes',
      schedule: 'Weekly on Sunday at 1:00 AM'
    },
    {
      id: '3',
      name: 'User Data Backup',
      type: 'differential',
      status: 'failed',
      lastRun: '2024-01-14 12:00:00',
      nextRun: '2024-01-15 12:00:00',
      size: '8.9 GB',
      duration: '45 minutes',
      schedule: 'Daily at 12:00 PM'
    }
  ]);

  const [backupHistory] = useState<BackupHistory[]>([
    {
      id: '1',
      name: 'Daily Database Backup',
      type: 'Incremental',
      timestamp: '2024-01-15 02:00:00',
      size: '2.3 GB',
      status: 'success',
      location: 'Cloud Storage'
    },
    {
      id: '2',
      name: 'User Data Backup',
      type: 'Differential',
      timestamp: '2024-01-14 12:00:00',
      size: '8.9 GB',
      status: 'failed',
      location: 'Local Storage'
    },
    {
      id: '3',
      name: 'Configuration Backup',
      type: 'Full',
      timestamp: '2024-01-14 03:00:00',
      size: '156 MB',
      status: 'success',
      location: 'Network Storage'
    },
    {
      id: '4',
      name: 'Weekly Full System Backup',
      type: 'Full',
      timestamp: '2024-01-08 01:00:00',
      size: '45.7 GB',
      status: 'success',
      location: 'Cloud Storage'
    },
    {
      id: '5',
      name: 'Application Data Backup',
      type: 'Incremental',
      timestamp: '2024-01-07 18:00:00',
      size: '1.2 GB',
      status: 'partial',
      location: 'Local Storage'
    }
  ]);

  const [storageLocations] = useState<StorageLocation[]>([
    {
      id: '1',
      name: 'Primary Cloud Storage',
      type: 'cloud',
      path: 's3://backup-bucket/primary/',
      available: '2.5 TB',
      used: '1.2 TB',
      status: 'online'
    },
    {
      id: '2',
      name: 'Local Backup Drive',
      type: 'local',
      path: '/backup/local/',
      available: '500 GB',
      used: '350 GB',
      status: 'online'
    },
    {
      id: '3',
      name: 'Network Attached Storage',
      type: 'network',
      path: '//nas-server/backups/',
      available: '1 TB',
      used: '750 GB',
      status: 'offline'
    }
  ]);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleToggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  const handleBackToAdmin = () => {
    navigate('/app/admin-dashboard');
  };

  const handleStartBackup = (jobId: string) => {
    setIsBackupRunning(true);
    setBackupProgress(0);
    
    // Simulate backup progress
    const interval = setInterval(() => {
      setBackupProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsBackupRunning(false);
          return 100;
        }
        return prev + 10;
      });
    }, 500);
  };

  const handleStopBackup = () => {
    setIsBackupRunning(false);
    setBackupProgress(0);
  };

  const handleRefresh = () => {
    setIsLoading(true);
    // Simulate refresh
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  const handleDownloadBackup = (backupId: string) => {
    console.log('Downloading backup:', backupId);
    // Here you would initiate the backup download
  };

  const handleDeleteBackup = (backupId: string) => {
    console.log('Deleting backup:', backupId);
    // Here you would delete the backup
  };

  const handleRestoreBackup = (backupId: string) => {
    setDialogType('restore');
    setOpenDialog(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'success':
      case 'online':
        return 'success';
      case 'running':
      case 'scheduled':
        return 'info';
      case 'failed':
      case 'error':
      case 'offline':
        return 'error';
      case 'partial':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
      case 'success':
        return <CheckCircle color="success" />;
      case 'failed':
      case 'error':
        return <Error color="error" />;
      case 'partial':
        return <Warning color="warning" />;
      case 'running':
        return <PlayArrow color="info" />;
      default:
        return <Schedule color="info" />;
    }
  };

  const getStorageIcon = (type: string) => {
    switch (type) {
      case 'cloud':
        return <CloudUpload />;
      case 'network':
        return <Storage />;
      default:
        return <Folder />;
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: 'background.default',
        transition: 'all 0.3s ease-in-out',
        ...(isMaximized && {
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 9999,
          bgcolor: 'background.paper'
        })
      }}
    >
      <Container
        maxWidth={isMaximized ? false : 'xl'}
        sx={{
          py: 3,
          px: isMaximized ? 3 : undefined,
          height: isMaximized ? '100vh' : 'auto',
          overflow: isMaximized ? 'auto' : 'visible'
        }}
      >
        <PageTitle
          title="Backup & Recovery"
          description="Manage system backups, recovery operations, and data protection"
          icon={<Backup />}
          breadcrumbs={[
            {
              label: 'Admin Dashboard',
              onClick: handleBackToAdmin
            },
            {
              label: 'Backup & Recovery'
            }
          ]}
          action={
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <Button
                variant="contained"
                startIcon={<Backup />}
                onClick={() => {
                  setDialogType('backup');
                  setOpenDialog(true);
                }}
              >
                Create Backup
              </Button>
              <Tooltip title="Refresh Data">
                <IconButton
                  onClick={handleRefresh}
                  sx={{
                    bgcolor: 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                >
                  <Refresh />
                </IconButton>
              </Tooltip>
              <Tooltip title={isMaximized ? 'Minimize' : 'Maximize'}>
                <IconButton
                  onClick={handleToggleMaximize}
                  sx={{
                    bgcolor: 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                >
                  {isMaximized ? <FullscreenExit /> : <FullscreenIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          }
        />

        {/* Backup Status Alert */}
        {isBackupRunning && (
          <Alert severity="info" sx={{ mb: 3 }}>
            <AlertTitle>Backup in Progress</AlertTitle>
            <Box sx={{ mt: 1 }}>
              <LinearProgress variant="determinate" value={backupProgress} />
              <Typography variant="body2" sx={{ mt: 1 }}>
                {backupProgress}% complete
              </Typography>
            </Box>
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Backup Jobs */}
          <Grid item xs={12} lg={8}>
            <Card>
              <CardHeader
                title="Backup Jobs"
                avatar={<Schedule />}
                subheader="Scheduled and on-demand backup operations"
                action={
                  <Button
                    size="small"
                    startIcon={<Settings />}
                    onClick={() => {
                      setDialogType('schedule');
                      setOpenDialog(true);
                    }}
                  >
                    Configure
                  </Button>
                }
              />
              <CardContent>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Job Name</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Last Run</TableCell>
                        <TableCell>Next Run</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {backupJobs.map((job) => (
                        <TableRow key={job.id} hover>
                          <TableCell>
                            <Box>
                              <Typography variant="subtitle2">
                                {job.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {job.schedule}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              size="small"
                              label={job.type}
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {getStatusIcon(job.status)}
                              <Chip
                                size="small"
                                label={job.status}
                                color={getStatusColor(job.status) as any}
                              />
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {job.lastRun}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {job.size} in {job.duration}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {job.nextRun}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              {job.status !== 'running' ? (
                                <IconButton
                                  size="small"
                                  color="primary"
                                  onClick={() => handleStartBackup(job.id)}
                                >
                                  <PlayArrow />
                                </IconButton>
                              ) : (
                                <IconButton
                                  size="small"
                                  color="error"
                                  onClick={handleStopBackup}
                                >
                                  <Stop />
                                </IconButton>
                              )}
                              <IconButton size="small">
                                <Settings />
                              </IconButton>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Storage Locations */}
          <Grid item xs={12} lg={4}>
            <Card>
              <CardHeader
                title="Storage Locations"
                avatar={<Storage />}
                subheader="Backup storage destinations"
              />
              <CardContent>
                <List>
                  {storageLocations.map((location, index) => (
                    <React.Fragment key={location.id}>
                      <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                        <ListItemIcon>
                          <Avatar sx={{ bgcolor: getStatusColor(location.status) === 'success' ? 'success.main' : 'error.main' }}>
                            {getStorageIcon(location.type)}
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="subtitle2">
                                {location.name}
                              </Typography>
                              <Chip
                                size="small"
                                label={location.status}
                                color={getStatusColor(location.status) as any}
                              />
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                {location.path}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                Used: {location.used} / Available: {location.available}
                              </Typography>
                            </Box>
                          }
                        />
                      </ListItem>
                      {index < storageLocations.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Backup History */}
          <Grid item xs={12}>
            <Card>
              <CardHeader
                title="Backup History"
                avatar={<History />}
                subheader="Recent backup operations and their status"
              />
              <CardContent>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Backup Name</TableCell>
                        <TableCell>Type</TableCell>
                        <TableCell>Timestamp</TableCell>
                        <TableCell>Size</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {backupHistory.map((backup) => (
                        <TableRow key={backup.id} hover>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                                <Database fontSize="small" />
                              </Avatar>
                              <Typography variant="subtitle2">
                                {backup.name}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip size="small" label={backup.type} variant="outlined" />
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {backup.timestamp}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {backup.size}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {getStatusIcon(backup.status)}
                              <Chip
                                size="small"
                                label={backup.status}
                                color={getStatusColor(backup.status) as any}
                              />
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {backup.location}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <IconButton
                                size="small"
                                color="primary"
                                onClick={() => handleDownloadBackup(backup.id)}
                              >
                                <Download />
                              </IconButton>
                              <IconButton
                                size="small"
                                color="success"
                                onClick={() => handleRestoreBackup(backup.id)}
                              >
                                <Restore />
                              </IconButton>
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleDeleteBackup(backup.id)}
                              >
                                <Delete />
                              </IconButton>
                            </Box>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Dialogs */}
        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>
            {dialogType === 'backup' && 'Create New Backup'}
            {dialogType === 'restore' && 'Restore from Backup'}
            {dialogType === 'schedule' && 'Configure Backup Schedule'}
          </DialogTitle>
          <DialogContent>
            {dialogType === 'backup' && (
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Backup Name"
                    placeholder="Enter backup name"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Backup Type</InputLabel>
                    <Select label="Backup Type">
                      <MenuItem value="full">Full Backup</MenuItem>
                      <MenuItem value="incremental">Incremental</MenuItem>
                      <MenuItem value="differential">Differential</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Storage Location</InputLabel>
                    <Select label="Storage Location">
                      <MenuItem value="cloud">Cloud Storage</MenuItem>
                      <MenuItem value="local">Local Storage</MenuItem>
                      <MenuItem value="network">Network Storage</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    multiline
                    rows={3}
                    placeholder="Optional backup description"
                  />
                </Grid>
              </Grid>
            )}
            {dialogType === 'restore' && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                <AlertTitle>Warning</AlertTitle>
                Restoring from backup will overwrite current data. This action cannot be undone.
                Please ensure you have a recent backup before proceeding.
              </Alert>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
            <Button variant="contained">
              {dialogType === 'backup' && 'Create Backup'}
              {dialogType === 'restore' && 'Restore'}
              {dialogType === 'schedule' && 'Save Schedule'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Box>
  );
};

export default BackupRecoveryPage;