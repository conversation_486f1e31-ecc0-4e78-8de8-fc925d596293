import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Breadcrumbs,
  Link,
  IconButton,
  Tooltip,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  AlertTitle,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Avatar,
  Divider,
  Tabs,
  Tab,
  Badge,
  LinearProgress
} from '@mui/material';
import {
  ArrowBack,
  FullscreenExit,
  Fullscreen as FullscreenIcon,
  Api,
  Key,
  Security,
  Speed,
  Analytics,
  Settings,
  Add,
  Edit,
  Delete,
  Visibility,
  VisibilityOff,
  ContentCopy,
  CheckCircle,
  Error,
  Warning,
  Info,
  TrendingUp,
  TrendingDown,
  Code,
  Description,
  Schedule,
  Block,
  VpnKey,
  Http,
  Storage,
  CloudQueue
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import PageTitle from '../../components/UI/PageTitle';

interface APIEndpoint {
  id: string;
  name: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  status: 'active' | 'inactive' | 'deprecated';
  version: string;
  description: string;
  rateLimit: number;
  authentication: 'none' | 'api-key' | 'oauth' | 'jwt';
  lastUsed: string;
  totalCalls: number;
  avgResponseTime: number;
}

interface APIKey {
  id: string;
  name: string;
  key: string;
  status: 'active' | 'inactive' | 'expired';
  permissions: string[];
  createdAt: string;
  expiresAt: string;
  lastUsed: string;
  totalRequests: number;
  rateLimit: number;
}

interface APIMetrics {
  totalRequests: number;
  successRate: number;
  avgResponseTime: number;
  errorRate: number;
  activeKeys: number;
  rateLimitHits: number;
}

const APIManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const [isMaximized, setIsMaximized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState<'endpoint' | 'key' | 'settings'>('endpoint');
  const [showApiKey, setShowApiKey] = useState<{[key: string]: boolean}>({});

  const [apiMetrics] = useState<APIMetrics>({
    totalRequests: 1234567,
    successRate: 98.5,
    avgResponseTime: 245,
    errorRate: 1.5,
    activeKeys: 23,
    rateLimitHits: 156
  });

  const [apiEndpoints] = useState<APIEndpoint[]>([
    {
      id: '1',
      name: 'User Authentication',
      path: '/api/v1/auth/login',
      method: 'POST',
      status: 'active',
      version: 'v1',
      description: 'Authenticate user credentials and return JWT token',
      rateLimit: 100,
      authentication: 'none',
      lastUsed: '2024-01-15 14:30:00',
      totalCalls: 45678,
      avgResponseTime: 120
    },
    {
      id: '2',
      name: 'Get User Profile',
      path: '/api/v1/users/profile',
      method: 'GET',
      status: 'active',
      version: 'v1',
      description: 'Retrieve authenticated user profile information',
      rateLimit: 1000,
      authentication: 'jwt',
      lastUsed: '2024-01-15 14:25:00',
      totalCalls: 123456,
      avgResponseTime: 85
    },
    {
      id: '3',
      name: 'Dataset Upload',
      path: '/api/v1/datasets/upload',
      method: 'POST',
      status: 'active',
      version: 'v1',
      description: 'Upload and process dataset files',
      rateLimit: 10,
      authentication: 'api-key',
      lastUsed: '2024-01-15 13:45:00',
      totalCalls: 8901,
      avgResponseTime: 2500
    },
    {
      id: '4',
      name: 'Statistical Analysis',
      path: '/api/v1/analysis/stats',
      method: 'POST',
      status: 'active',
      version: 'v1',
      description: 'Perform statistical analysis on dataset',
      rateLimit: 50,
      authentication: 'jwt',
      lastUsed: '2024-01-15 12:15:00',
      totalCalls: 34567,
      avgResponseTime: 1800
    },
    {
      id: '5',
      name: 'Legacy User Data',
      path: '/api/v0/users',
      method: 'GET',
      status: 'deprecated',
      version: 'v0',
      description: 'Legacy endpoint for user data (deprecated)',
      rateLimit: 100,
      authentication: 'api-key',
      lastUsed: '2024-01-10 09:30:00',
      totalCalls: 1234,
      avgResponseTime: 300
    }
  ]);

  const [apiKeys] = useState<APIKey[]>([
    {
      id: '1',
      name: 'Production App Key',
      key: 'sk_prod_1234567890abcdef',
      status: 'active',
      permissions: ['read', 'write', 'admin'],
      createdAt: '2024-01-01 10:00:00',
      expiresAt: '2024-12-31 23:59:59',
      lastUsed: '2024-01-15 14:30:00',
      totalRequests: 567890,
      rateLimit: 10000
    },
    {
      id: '2',
      name: 'Mobile App Key',
      key: 'sk_mobile_abcdef1234567890',
      status: 'active',
      permissions: ['read', 'write'],
      createdAt: '2024-01-05 15:30:00',
      expiresAt: '2024-06-30 23:59:59',
      lastUsed: '2024-01-15 13:45:00',
      totalRequests: 234567,
      rateLimit: 5000
    },
    {
      id: '3',
      name: 'Analytics Service',
      key: 'sk_analytics_fedcba0987654321',
      status: 'active',
      permissions: ['read'],
      createdAt: '2024-01-10 09:00:00',
      expiresAt: '2024-03-31 23:59:59',
      lastUsed: '2024-01-15 12:00:00',
      totalRequests: 123456,
      rateLimit: 2000
    },
    {
      id: '4',
      name: 'Test Environment',
      key: 'sk_test_1111222233334444',
      status: 'inactive',
      permissions: ['read'],
      createdAt: '2024-01-08 11:00:00',
      expiresAt: '2024-02-28 23:59:59',
      lastUsed: '2024-01-12 16:20:00',
      totalRequests: 5678,
      rateLimit: 1000
    },
    {
      id: '5',
      name: 'Legacy Integration',
      key: 'sk_legacy_aaaa1111bbbb2222',
      status: 'expired',
      permissions: ['read', 'write'],
      createdAt: '2023-12-01 08:00:00',
      expiresAt: '2024-01-01 00:00:00',
      lastUsed: '2023-12-31 23:45:00',
      totalRequests: 98765,
      rateLimit: 3000
    }
  ]);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleToggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  const handleBackToAdmin = () => {
    navigate('/app/admin-dashboard');
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleToggleApiKeyVisibility = (keyId: string) => {
    setShowApiKey(prev => ({
      ...prev,
      [keyId]: !prev[keyId]
    }));
  };

  const handleCopyApiKey = (key: string) => {
    navigator.clipboard.writeText(key);
    // You could show a toast notification here
  };

  const handleCreateEndpoint = () => {
    setDialogType('endpoint');
    setOpenDialog(true);
  };

  const handleCreateApiKey = () => {
    setDialogType('key');
    setOpenDialog(true);
  };

  const handleEditEndpoint = (endpointId: string) => {
    console.log('Editing endpoint:', endpointId);
  };

  const handleDeleteEndpoint = (endpointId: string) => {
    console.log('Deleting endpoint:', endpointId);
  };

  const handleRevokeApiKey = (keyId: string) => {
    console.log('Revoking API key:', keyId);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'deprecated':
      case 'expired':
        return 'error';
      default:
        return 'default';
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET':
        return 'info';
      case 'POST':
        return 'success';
      case 'PUT':
        return 'warning';
      case 'DELETE':
        return 'error';
      case 'PATCH':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const maskApiKey = (key: string) => {
    return key.substring(0, 8) + '...' + key.substring(key.length - 4);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: 'background.default',
        transition: 'all 0.3s ease-in-out',
        ...(isMaximized && {
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 9999,
          bgcolor: 'background.paper'
        })
      }}
    >
      <Container
        maxWidth={isMaximized ? false : 'xl'}
        sx={{
          py: 3,
          px: isMaximized ? 3 : undefined,
          height: isMaximized ? '100vh' : 'auto',
          overflow: isMaximized ? 'auto' : 'visible'
        }}
      >
        <PageTitle
          title="API Management"
          description="Manage API endpoints, authentication keys, and monitor usage"
          icon={<Api />}
          breadcrumbs={[
            {
              label: 'Admin Dashboard',
              onClick: handleBackToAdmin
            },
            {
              label: 'API Management'
            }
          ]}
          action={
            <Tooltip title={isMaximized ? 'Minimize' : 'Maximize'}>
              <IconButton
                onClick={handleToggleMaximize}
                sx={{
                  bgcolor: 'action.hover',
                  '&:hover': { bgcolor: 'action.selected' }
                }}
              >
                {isMaximized ? <FullscreenExit /> : <FullscreenIcon />}
              </IconButton>
            </Tooltip>
          }
        />

        {/* API Metrics Overview */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary.main" sx={{ fontWeight: 600 }}>
                  {formatNumber(apiMetrics.totalRequests)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Requests
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                  <TrendingUp color="success" fontSize="small" />
                  <Typography variant="caption" color="success.main" sx={{ ml: 0.5 }}>
                    +12.5%
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
                  {apiMetrics.successRate}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Success Rate
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                  <TrendingUp color="success" fontSize="small" />
                  <Typography variant="caption" color="success.main" sx={{ ml: 0.5 }}>
                    +0.3%
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="info.main" sx={{ fontWeight: 600 }}>
                  {apiMetrics.avgResponseTime}ms
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Avg Response
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                  <TrendingDown color="success" fontSize="small" />
                  <Typography variant="caption" color="success.main" sx={{ ml: 0.5 }}>
                    -15ms
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="error.main" sx={{ fontWeight: 600 }}>
                  {apiMetrics.errorRate}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Error Rate
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                  <TrendingDown color="success" fontSize="small" />
                  <Typography variant="caption" color="success.main" sx={{ ml: 0.5 }}>
                    -0.2%
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main" sx={{ fontWeight: 600 }}>
                  {apiMetrics.activeKeys}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Active Keys
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                  <TrendingUp color="info" fontSize="small" />
                  <Typography variant="caption" color="info.main" sx={{ ml: 0.5 }}>
                    +2
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="secondary.main" sx={{ fontWeight: 600 }}>
                  {formatNumber(apiMetrics.rateLimitHits)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Rate Limit Hits
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                  <TrendingUp color="warning" fontSize="small" />
                  <Typography variant="caption" color="warning.main" sx={{ ml: 0.5 }}>
                    +23
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Tabs value={activeTab} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Api />
                  Endpoints
                  <Badge badgeContent={apiEndpoints.length} color="primary" />
                </Box>
              }
            />
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Key />
                  API Keys
                  <Badge badgeContent={apiKeys.filter(k => k.status === 'active').length} color="success" />
                </Box>
              }
            />
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Analytics />
                  Analytics
                </Box>
              }
            />
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Settings />
                  Settings
                </Box>
              }
            />
          </Tabs>
        </Paper>

        {/* Tab Content */}
        {activeTab === 0 && (
          <Card>
            <CardHeader
              title="API Endpoints"
              subheader="Manage and monitor API endpoints"
              action={
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={handleCreateEndpoint}
                >
                  Add Endpoint
                </Button>
              }
            />
            <CardContent>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Endpoint</TableCell>
                      <TableCell>Method</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Authentication</TableCell>
                      <TableCell>Rate Limit</TableCell>
                      <TableCell>Usage</TableCell>
                      <TableCell>Performance</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {apiEndpoints.map((endpoint) => (
                      <TableRow key={endpoint.id} hover>
                        <TableCell>
                          <Box>
                            <Typography variant="subtitle2" sx={{ fontFamily: 'monospace' }}>
                              {endpoint.path}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {endpoint.name} • {endpoint.version}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            size="small"
                            label={endpoint.method}
                            color={getMethodColor(endpoint.method) as any}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            size="small"
                            label={endpoint.status}
                            color={getStatusColor(endpoint.status) as any}
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {endpoint.authentication === 'none' ? <Block fontSize="small" /> : <Security fontSize="small" />}
                            <Typography variant="body2">
                              {endpoint.authentication}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {endpoint.rateLimit}/min
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2">
                              {formatNumber(endpoint.totalCalls)} calls
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Last: {endpoint.lastUsed}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2">
                              {endpoint.avgResponseTime}ms avg
                            </Typography>
                            <LinearProgress
                              variant="determinate"
                              value={Math.min((endpoint.avgResponseTime / 3000) * 100, 100)}
                              sx={{ mt: 0.5, height: 4 }}
                              color={endpoint.avgResponseTime < 500 ? 'success' : endpoint.avgResponseTime < 1500 ? 'warning' : 'error'}
                            />
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleEditEndpoint(endpoint.id)}
                            >
                              <Edit />
                            </IconButton>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleDeleteEndpoint(endpoint.id)}
                            >
                              <Delete />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        )}

        {activeTab === 1 && (
          <Card>
            <CardHeader
              title="API Keys"
              subheader="Manage authentication keys and permissions"
              action={
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={handleCreateApiKey}
                >
                  Generate Key
                </Button>
              }
            />
            <CardContent>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Key Name</TableCell>
                      <TableCell>API Key</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Permissions</TableCell>
                      <TableCell>Usage</TableCell>
                      <TableCell>Expires</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {apiKeys.map((apiKey) => (
                      <TableRow key={apiKey.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                              <VpnKey fontSize="small" />
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle2">
                                {apiKey.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                Created: {apiKey.createdAt}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                              {showApiKey[apiKey.id] ? apiKey.key : maskApiKey(apiKey.key)}
                            </Typography>
                            <IconButton
                              size="small"
                              onClick={() => handleToggleApiKeyVisibility(apiKey.id)}
                            >
                              {showApiKey[apiKey.id] ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => handleCopyApiKey(apiKey.key)}
                            >
                              <ContentCopy />
                            </IconButton>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            size="small"
                            label={apiKey.status}
                            color={getStatusColor(apiKey.status) as any}
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                            {apiKey.permissions.map((permission) => (
                              <Chip
                                key={permission}
                                size="small"
                                label={permission}
                                variant="outlined"
                              />
                            ))}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2">
                              {formatNumber(apiKey.totalRequests)} requests
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Limit: {formatNumber(apiKey.rateLimit)}/hour
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {apiKey.expiresAt}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={() => handleEditEndpoint(apiKey.id)}
                            >
                              <Edit />
                            </IconButton>
                            <IconButton
                              size="small"
                              color="error"
                              onClick={() => handleRevokeApiKey(apiKey.id)}
                            >
                              <Block />
                            </IconButton>
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        )}

        {activeTab === 2 && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Alert severity="info">
                <AlertTitle>Analytics Dashboard</AlertTitle>
                Detailed API analytics and usage metrics will be displayed here.
                This includes request patterns, error analysis, and performance trends.
              </Alert>
            </Grid>
          </Grid>
        )}

        {activeTab === 3 && (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Alert severity="info">
                <AlertTitle>API Settings</AlertTitle>
                Global API configuration settings, rate limiting policies, and security options will be available here.
              </Alert>
            </Grid>
          </Grid>
        )}

        {/* Dialogs */}
        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>
            {dialogType === 'endpoint' && 'Create New API Endpoint'}
            {dialogType === 'key' && 'Generate New API Key'}
            {dialogType === 'settings' && 'API Settings'}
          </DialogTitle>
          <DialogContent>
            {dialogType === 'endpoint' && (
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Endpoint Name"
                    placeholder="Enter endpoint name"
                  />
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    label="Path"
                    placeholder="/api/v1/example"
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth>
                    <InputLabel>Method</InputLabel>
                    <Select label="Method">
                      <MenuItem value="GET">GET</MenuItem>
                      <MenuItem value="POST">POST</MenuItem>
                      <MenuItem value="PUT">PUT</MenuItem>
                      <MenuItem value="DELETE">DELETE</MenuItem>
                      <MenuItem value="PATCH">PATCH</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Authentication</InputLabel>
                    <Select label="Authentication">
                      <MenuItem value="none">None</MenuItem>
                      <MenuItem value="api-key">API Key</MenuItem>
                      <MenuItem value="jwt">JWT Token</MenuItem>
                      <MenuItem value="oauth">OAuth</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Rate Limit (per minute)"
                    type="number"
                    defaultValue={100}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    multiline
                    rows={3}
                    placeholder="Describe what this endpoint does"
                  />
                </Grid>
              </Grid>
            )}
            {dialogType === 'key' && (
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Key Name"
                    placeholder="Enter a descriptive name for this key"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Rate Limit (per hour)"
                    type="number"
                    defaultValue={1000}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Expires In (days)"
                    type="number"
                    defaultValue={365}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Permissions
                  </Typography>
                  <FormControlLabel
                    control={<Switch defaultChecked />}
                    label="Read Access"
                  />
                  <FormControlLabel
                    control={<Switch />}
                    label="Write Access"
                  />
                  <FormControlLabel
                    control={<Switch />}
                    label="Admin Access"
                  />
                </Grid>
              </Grid>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
            <Button variant="contained">
              {dialogType === 'endpoint' && 'Create Endpoint'}
              {dialogType === 'key' && 'Generate Key'}
              {dialogType === 'settings' && 'Save Settings'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Box>
  );
};

export default APIManagementPage;