-- Fix create_guest_user function to accept additional parameters
-- This addresses the parameter mismatch error when calling the function

CREATE OR REPLACE FUNCTION public.create_guest_user(
    p_email VARCHAR(255),
    p_full_name VARCHAR(255) DEFAULT NULL,
    p_institution VARCHAR(255) DEFAULT NULL,
    p_marketing_consent BOOLEAN DEFAULT FALSE
) RETURNS TABLE(
    guest_id UUID,
    verification_token VARCHAR(255),
    success BOOLEAN,
    message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_guest_id UUID;
    v_token VARCHAR(255);
    v_existing_user UUID;
BEGIN
    -- Check if email already exists
    SELECT id INTO v_existing_user 
    FROM public.guest_users 
    WHERE email = p_email;
    
    IF v_existing_user IS NOT NULL THEN
        RETURN QUERY SELECT 
            v_existing_user,
            NULL::VARCHAR(255),
            FALSE,
            'Email already registered'::TEXT;
        RETURN;
    END IF;
    
    -- Generate verification token
    v_token := encode(gen_random_bytes(32), 'hex');
    
    -- Insert guest user
    INSERT INTO public.guest_users (
        email, 
        verification_token, 
        verification_expires_at,
        marketing_consent
    ) VALUES (
        p_email, 
        v_token, 
        NOW() + INTERVAL '24 hours',
        p_marketing_consent
    ) RETURNING id INTO v_guest_id;
    
    RETURN QUERY SELECT 
        v_guest_id,
        v_token,
        TRUE,
        'Guest user created successfully'::TEXT;
        
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT 
            NULL::UUID,
            NULL::VARCHAR(255),
            FALSE,
            SQLERRM::TEXT;
END;
$$;