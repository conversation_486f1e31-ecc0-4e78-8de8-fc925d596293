import React, { useState } from 'react';
// Removed useAuth import as AppHeader is for logged-out state
import {
  AppB<PERSON>,
  <PERSON><PERSON><PERSON>,
  Typography,
  Button,
  IconButton,
  Box,
  useTheme,
  useMediaQuery,
  Tooltip,
  Menu,
  MenuItem,
  Avatar,
  Badge,
  alpha,
  SwipeableDrawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import { useAppTheme } from '../../context/ThemeContext';
import {
  BarChart as BarChartIcon,
  Assessment as AssessmentIcon,
  B<PERSON>ble<PERSON>hart as BubbleChartIcon,
  TableChart as TableChartIcon,
  Menu as MenuIcon,
  GitHub as GitHubIcon,
  Dashboard as DashboardIcon,
  Notifications as NotificationsIcon,
  Brightness4 as Brightness4Icon,
  Brightness7 as Brightness7Icon,
  HelpOutline as HelpOutlineIcon,
  Person as PersonIcon,
  Settings as SettingsIcon,
  ExitToApp as ExitToAppIcon,
  Inbox as InboxIcon,
  MoreVert as MoreVertIcon,
  Storage as StorageIcon,
  Functions as FunctionsIcon,
  ShowChart as ShowChartIcon,
  Save as SaveIcon,
  Home as HomeIcon,
  Lightbulb as LightbulbIcon, // Add LightbulbIcon import
  Email as EmailIcon, // Import EmailIcon
  Login as LoginIcon, // Import LoginIcon for the button
  Facebook as FacebookIcon, // Import social icons
  Twitter as TwitterIcon,
  YouTube as YouTubeIcon,
  LinkedIn as LinkedInIcon
} from '@mui/icons-material';
import { UpdateButton } from '../PWA';

interface AppHeaderProps {
  title: string;
  onMenuClick: () => void;
  onNavigate: (path: string) => void; 
  isGuest?: boolean; // Add isGuest prop
  logoutGuest?: () => void; // Add logoutGuest prop
}

const AppHeader: React.FC<AppHeaderProps> = ({
  title,
  onMenuClick,
  onNavigate,
  isGuest = false, // Default to false
  logoutGuest
}) => {
  const theme = useTheme();
  const { themeMode, setThemeMode, appliedTheme } = useAppTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('md'));
  
  // Removed userAnchorEl state, AppHeader is for logged-out state
  const [moreMenuAnchorEl, setMoreMenuAnchorEl] = useState<null | HTMLElement>(null);
  // Removed notificationsAnchorEl as well, assuming notifications are user-specific for now
  const [quickMenuOpen, setQuickMenuOpen] = useState(false);

  // Removed handleNotificationsClick, handleNotificationsClose, handleUserMenuClick, handleUserMenuClose
  // as these are related to logged-in state UI elements not present in logged-out AppHeader.

  const handleMoreMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setMoreMenuAnchorEl(event.currentTarget);
  };
  
  const handleMoreMenuClose = () => {
    setMoreMenuAnchorEl(null);
  };
  
  const toggleQuickMenu = (open: boolean) => () => {
    setQuickMenuOpen(open);
  };

  // Theme toggle handler
  const handleThemeToggle = () => {
    const nextTheme = appliedTheme === 'light' ? 'dark' : 'light';
    setThemeMode(nextTheme);
  };

  // Define navigation items for the more menu and quick menu
  const navItems = [
    { name: 'Home', icon: <HomeIcon />, path: '/home' },
    { name: 'Data Management', icon: <StorageIcon />, path: '/data-management/import' },
    { name: 'Descriptive Statistics', icon: <FunctionsIcon />, path: '/stats/descriptives' },
    { name: 'Inferential Statistics', icon: <AssessmentIcon />, path: '/inferential-stats/ttest' },
    { name: 'Data Visualization', icon: <ShowChartIcon />, path: '/charts' },
    { name: 'Advanced Analysis', icon: <SaveIcon />, path: '/advanced-analysis' },
    { name: 'Help & Resources', icon: <HelpOutlineIcon />, path: '/knowledge-base' },
  ];
  
  // Handle navigation click
  const handleNavClick = (path: string) => {
    const internalPath = path.replace(/^\/#\//, '');
    onNavigate(internalPath);
    handleMoreMenuClose();
    setQuickMenuOpen(false);
  };

  // Handle Login/Exit Guest button click
  const handleAuthButtonClick = () => {
    if (isGuest && logoutGuest) {
      logoutGuest(); // Log out the guest first
    }
    onNavigate('auth/login'); // Then navigate to login
    handleMoreMenuClose(); // Close menu if open
  };

  const authButtonText = isGuest ? "Exit Guest & Login" : "Login / Sign Up";

  return (
    <AppBar 
      position="fixed" 
      sx={{ 
        zIndex: theme.zIndex.drawer + 1,
        boxShadow: '0 2px 10px rgba(0,0,0,0.08)', // Enhanced shadow
        background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
      }}
      elevation={0}
    >
      <Toolbar sx={{ 
        minHeight: { xs: 56, sm: 64 }, // Smaller toolbar on mobile
        px: { xs: 1, sm: 2 } // Less padding on mobile
      }}>
        <IconButton
          color="inherit"
          aria-label="open drawer"
          edge="start"
          onClick={onMenuClick}
          sx={{ mr: 1 }}
          size={isMobile ? "small" : "medium"}
        >
          <MenuIcon />
        </IconButton>
        
        {/* Wrap Logo and Title in a clickable Box */}
        <Box
          onClick={() => onNavigate('/app/dashboard')} // Navigate to dashboard on click
          sx={{
            display: 'flex',
            alignItems: 'center',
            mr: 1,
            cursor: 'pointer', // Indicate clickability
            textDecoration: 'none', // Remove potential underline if wrapped in link later
            color: 'inherit' // Inherit color
          }}
        >
          <Box 
            component="img"
            src="/logo.png"
            alt="DataStatPro Logo"
            sx={{ 
              height: { xs: 28, sm: 32 },
              width: { xs: 28, sm: 32 },
              mr: 1.5,
              borderRadius: '8px',
              backgroundColor: 'white',
              padding: '4px'
            }}
          />
          <Typography 
            variant={isMobile ? "subtitle1" : "h6"} 
            noWrap 
            component="div" 
            sx={{ 
              fontWeight: 'bold',
              letterSpacing: '0.5px'
            }}
          >
            {title}
          </Typography>
        </Box>

        {/* Empty Box to allow flexGrow */}
        <Box sx={{ flexGrow: 1 }} /> 

        {/* Desktop Navigation & Actions */}
        <Box sx={{ 
          display: { xs: 'none', md: 'flex' }, 
          alignItems: 'center',
          '& .MuiIconButton-root': {
            mx: { xs: 0.5, sm: 0.75 }, // Less space on mobile
            p: { xs: 0.75, sm: 1 } // Smaller buttons on mobile
          }
        }}>
          <Tooltip title="Data Management">
            <IconButton 
              color="inherit" 
              size="medium"
              onClick={() => handleNavClick('/data-management/import')}
            >
              <TableChartIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Descriptive Statistics">
            <IconButton 
              color="inherit" 
              size="medium"
              onClick={() => handleNavClick('/stats/descriptives')}
            >
              <AssessmentIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Inferential Statistics">
            <IconButton 
              color="inherit" 
              size="medium"
              onClick={() => handleNavClick('/inferential-stats/ttest')}
            >
              <BarChartIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Data Visualization">
            <IconButton 
              color="inherit" 
              size="medium"
              onClick={() => handleNavClick('/charts')}
            >
              <BubbleChartIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Help">
            <IconButton
              color="inherit"
              size="medium"
              onClick={() => handleNavClick('/knowledge-base')}
            >
              <HelpOutlineIcon />
            </IconButton>
          </Tooltip>
          
          {/* Removed Notifications Icon from desktop nav for logged-out state */}

          {/* Social Icons */}
          <Box sx={{ ml: 2, borderLeft: 1, borderColor: 'divider', pl: 1 }}>
            <Tooltip title="Email">
              <IconButton color="inherit" component="a" href="mailto:<EMAIL>" aria-label="Email">
                <EmailIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Facebook">
              <IconButton color="inherit" component="a" href="https://www.facebook.com/datastatpro/" target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                <FacebookIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Twitter">
              <IconButton color="inherit" component="a" href="https://x.com/datastatpro" target="_blank" rel="noopener noreferrer" aria-label="Twitter">
                <TwitterIcon />
              </IconButton>
            </Tooltip>
             <Tooltip title="YouTube">
              <IconButton color="inherit" component="a" href="https://www.youtube.com/watch?v=HF-gWGcZPBo&list=PLHQA3dbkjl7vusT1AceUmJGLQD2vlE42v" target="_blank" rel="noopener noreferrer" aria-label="YouTube">
                <YouTubeIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="LinkedIn">
              <IconButton color="inherit" component="a" href="https://www.linkedin.com/in/nadeem-shafique-butt-99a41418/" target="_blank" rel="noopener noreferrer" aria-label="LinkedIn">
                <LinkedInIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Mobile Navigation - Theme Toggle, More menu icon & Login/Exit Guest Button */}
        <Box sx={{ display: { xs: 'flex', md: 'none' }, alignItems: 'center' }}>
          {/* Theme Toggle for Mobile */}
          <Tooltip title={`Switch to ${appliedTheme === 'light' ? 'dark' : 'light'} theme`}>
            <IconButton
              color="inherit"
              onClick={handleThemeToggle}
              size="small"
              sx={{ mr: 1 }}
            >
              {appliedTheme === 'light' ? <Brightness4Icon /> : <Brightness7Icon />}
            </IconButton>
          </Tooltip>

          {/* Combined Button for sm screens */}
          <Button
            color="inherit"
            onClick={handleAuthButtonClick}
            startIcon={<LoginIcon />}
            sx={{ mr: 1, display: { xs: 'none', sm: 'flex'} }}
          >
            {isGuest ? "Exit Guest" : "Login"} {/* Shorter text for mobile */}
          </Button>
          {/* Icon button for xs screens */}
          <Tooltip title={authButtonText}>
            <IconButton
              color="inherit"
              onClick={handleAuthButtonClick}
              sx={{ display: { xs: 'flex', sm: 'none'}, mr: 1 }}
            >
              <LoginIcon />
            </IconButton>
          </Tooltip>

          {/* PWA Update Button */}
          <UpdateButton variant="menu" size="small" />

          <Tooltip title="Quick Navigation">
            <IconButton
              color="inherit"
              size="small"
              onClick={handleMoreMenuClick}
            >
              <MoreVertIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
        
        {/* More Menu for Mobile (remains for general nav items) */}
        <Menu
          anchorEl={moreMenuAnchorEl}
          open={Boolean(moreMenuAnchorEl)}
          onClose={handleMoreMenuClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          PaperProps={{
            sx: {
              maxHeight: '70vh',
              width: 200
            }
          }}
        >
          {navItems.map((item) => (
            <MenuItem 
              key={item.name} 
              onClick={() => handleNavClick(item.path)}
              sx={{ py: 1 }}
            >
              <ListItemIcon sx={{ minWidth: 36 }}>
                {item.icon}
              </ListItemIcon>
              <ListItemText primary={item.name} />
            </MenuItem>
          ))}
           {/* Add Login/Exit Guest to mobile menu */}
           <>
             <Divider />
             <MenuItem 
               onClick={handleAuthButtonClick}
               sx={{ py: 1 }}
             >
               <ListItemIcon sx={{ minWidth: 36 }}>
                 <LoginIcon />
               </ListItemIcon>
               <ListItemText primary={authButtonText} />
             </MenuItem>
           </>
        </Menu>
        
        {/* Theme Toggle and Login/Exit Guest Button for Desktop */}
        <Box sx={{ display: { xs: 'none', md: 'flex' }, alignItems: 'center' }}>
          <Tooltip title={`Switch to ${appliedTheme === 'light' ? 'dark' : 'light'} theme`}>
            <IconButton
              color="inherit"
              onClick={handleThemeToggle}
              sx={{ mr: 1 }}
            >
              {appliedTheme === 'light' ? <Brightness4Icon /> : <Brightness7Icon />}
            </IconButton>
          </Tooltip>
          <Button
            color="inherit"
            onClick={handleAuthButtonClick}
            startIcon={<LoginIcon />}
            sx={{ ml: 1 }}
          >
            {authButtonText}
          </Button>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default AppHeader;
