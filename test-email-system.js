/**
 * Email System Test Script
 * 
 * This script helps test the email delivery system after applying fixes.
 * Run this in the browser console on your DataStatPro application.
 */

// Test configuration
const TEST_EMAIL = '<EMAIL>'; // Replace with your test email
const SUPABASE_URL = 'https://your-project-ref.supabase.co'; // Replace with your Supabase URL
const SUPABASE_ANON_KEY = 'your-anon-key'; // Replace with your anon key

/**
 * Test 1: Database Diagnostics
 * This tests the database configuration and extensions
 */
async function testDatabaseDiagnostics() {
    console.log('🔍 Testing Database Diagnostics...');
    
    try {
        const { createClient } = supabase;
        const client = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        const { data, error } = await client.rpc('diagnose_email_system');
        
        if (error) {
            console.error('❌ Database diagnostics failed:', error);
            return false;
        }
        
        console.log('📊 Diagnostic Results:');
        console.table(data);
        
        // Check if all components are properly configured
        const issues = data.filter(item => item.status !== 'ENABLED' && item.status !== 'CONFIGURED' && item.status !== 'REACHABLE');
        
        if (issues.length === 0) {
            console.log('✅ All database components are properly configured!');
            return true;
        } else {
            console.log('⚠️ Issues found:');
            issues.forEach(issue => {
                console.log(`- ${issue.component}: ${issue.status} - ${issue.action_required}`);
            });
            return false;
        }
        
    } catch (err) {
        console.error('❌ Diagnostic test failed:', err);
        return false;
    }
}

/**
 * Test 2: Guest User Creation
 * This tests the complete guest user creation flow
 */
async function testGuestUserCreation() {
    console.log('👤 Testing Guest User Creation...');
    
    try {
        const { createClient } = supabase;
        const client = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        // Use the improved function for better error reporting
        const { data, error } = await client.rpc('create_guest_user_v2', {
            p_email: TEST_EMAIL,
            p_full_name: 'Test User',
            p_institution: 'Test Institution',
            p_marketing_consent: false
        });
        
        if (error) {
            console.error('❌ Guest user creation failed:', error);
            return false;
        }
        
        console.log('📧 Guest User Creation Results:');
        console.table(data);
        
        const result = data[0];
        if (result.success && result.email_sent) {
            console.log('✅ Guest user created and email sent successfully!');
            console.log(`📬 Check ${TEST_EMAIL} for the verification code: ${result.verification_token}`);
            return true;
        } else {
            console.log('⚠️ Guest user created but email failed:');
            console.log(`- Success: ${result.success}`);
            console.log(`- Email Sent: ${result.email_sent}`);
            console.log(`- Message: ${result.message}`);
            console.log(`- Email Error: ${result.email_error}`);
            return false;
        }
        
    } catch (err) {
        console.error('❌ Guest user creation test failed:', err);
        return false;
    }
}

/**
 * Test 3: Edge Function Direct Test
 * This tests the Edge Function directly
 */
async function testEdgeFunctionDirect() {
    console.log('🔧 Testing Edge Function Directly...');
    
    try {
        const response = await fetch(`${SUPABASE_URL}/functions/v1/send-guest-verification-email`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: TEST_EMAIL,
                verificationCode: '123456',
                guestId: '00000000-0000-0000-0000-000000000000'
            })
        });
        
        const result = await response.text();
        
        console.log(`📡 Edge Function Response (${response.status}):`);
        console.log(result);
        
        if (response.status === 200) {
            console.log('✅ Edge Function is working correctly!');
            return true;
        } else if (response.status === 400) {
            console.log('⚠️ Edge Function reachable but returned validation error (expected for test data)');
            return true;
        } else {
            console.log('❌ Edge Function returned error status');
            return false;
        }
        
    } catch (err) {
        console.error('❌ Edge Function test failed:', err);
        return false;
    }
}

/**
 * Run All Tests
 * This runs all tests in sequence
 */
async function runAllTests() {
    console.log('🚀 Starting Email System Tests...');
    console.log('=' .repeat(50));
    
    // Update test email if needed
    if (TEST_EMAIL === '<EMAIL>') {
        console.log('⚠️ Please update TEST_EMAIL in the script with your actual email address');
        return;
    }
    
    const results = {
        diagnostics: false,
        guestCreation: false,
        edgeFunction: false
    };
    
    // Test 1: Database Diagnostics
    results.diagnostics = await testDatabaseDiagnostics();
    console.log('');
    
    // Test 2: Guest User Creation
    results.guestCreation = await testGuestUserCreation();
    console.log('');
    
    // Test 3: Edge Function Direct Test
    results.edgeFunction = await testEdgeFunctionDirect();
    console.log('');
    
    // Summary
    console.log('📋 Test Summary:');
    console.log('=' .repeat(50));
    console.log(`Database Diagnostics: ${results.diagnostics ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Guest User Creation: ${results.guestCreation ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Edge Function Direct: ${results.edgeFunction ? '✅ PASS' : '❌ FAIL'}`);
    
    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
        console.log('');
        console.log('🎉 All tests passed! Email system should be working correctly.');
        console.log('📧 Try the guest registration flow in your application.');
    } else {
        console.log('');
        console.log('⚠️ Some tests failed. Please check the issues above and refer to EMAIL_DELIVERY_FIX_GUIDE.md');
    }
}

/**
 * Quick Setup Instructions
 */
function showSetupInstructions() {
    console.log('📋 Quick Setup Instructions:');
    console.log('=' .repeat(50));
    console.log('1. Update the configuration variables at the top of this script');
    console.log('2. Make sure you have applied the database migration');
    console.log('3. Configure database settings (app.supabase_url, app.service_role_key)');
    console.log('4. Set Edge Function environment variables (RESEND_API_KEY, FROM_EMAIL)');
    console.log('5. Deploy the Edge Function');
    console.log('6. Run: runAllTests()');
    console.log('');
    console.log('For detailed instructions, see: EMAIL_DELIVERY_FIX_GUIDE.md');
}

// Export functions for manual testing
window.emailSystemTest = {
    runAllTests,
    testDatabaseDiagnostics,
    testGuestUserCreation,
    testEdgeFunctionDirect,
    showSetupInstructions
};

// Show instructions on load
showSetupInstructions();

console.log('');
console.log('🔧 Email System Test Script Loaded!');
console.log('Run: emailSystemTest.runAllTests() to start testing');
console.log('Or run individual tests: emailSystemTest.testDatabaseDiagnostics()');
