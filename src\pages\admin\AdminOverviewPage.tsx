import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  LinearProgress,
  Alert,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit,
  TrendingUp,
  TrendingDown,
  People,
  Dataset,
  Storage,
  Security,
  Warning,
  CheckCircle,
  Error,
  Info,
  Notifications,
  Schedule,
  CloudUpload,
  Analytics,
  AdminPanelSettings,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import PageTitle from '../../components/UI/PageTitle';

interface SystemMetric {
  id: string;
  title: string;
  value: string | number;
  change: number;
  changeType: 'increase' | 'decrease';
  icon: React.ReactNode;
  color: string;
}

interface RecentActivity {
  id: string;
  type: 'user_registration' | 'dataset_upload' | 'system_alert' | 'quota_exceeded';
  message: string;
  timestamp: string;
  severity: 'info' | 'warning' | 'error' | 'success';
}

interface SystemAlert {
  id: string;
  title: string;
  message: string;
  severity: 'info' | 'warning' | 'error';
  timestamp: string;
  resolved: boolean;
}

interface AdminOverviewPageProps {}

const AdminOverviewPage: React.FC<AdminOverviewPageProps> = () => {
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const [isMaximized, setIsMaximized] = useState(false);
  const [metrics, setMetrics] = useState<SystemMetric[]>([]);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [systemAlerts, setSystemAlerts] = useState<SystemAlert[]>([]);
  const [loading, setLoading] = useState(true);

  const handleRefresh = () => {
    setLoading(true);
    // Simulate refresh
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  // Mock data for demonstration
  useEffect(() => {
    const mockMetrics: SystemMetric[] = [
      {
        id: '1',
        title: 'Total Users',
        value: 1247,
        change: 12.5,
        changeType: 'increase',
        icon: <People />,
        color: '#1976d2'
      },
      {
        id: '2',
        title: 'Active Datasets',
        value: 3456,
        change: 8.3,
        changeType: 'increase',
        icon: <Dataset />,
        color: '#2e7d32'
      },
      {
        id: '3',
        title: 'Storage Used',
        value: '2.4 TB',
        change: -5.2,
        changeType: 'decrease',
        icon: <Storage />,
        color: '#ed6c02'
      },
      {
        id: '4',
        title: 'System Uptime',
        value: '99.9%',
        change: 0.1,
        changeType: 'increase',
        icon: <Security />,
        color: '#9c27b0'
      }
    ];

    const mockActivity: RecentActivity[] = [
      {
        id: '1',
        type: 'user_registration',
        message: 'New user registered: <EMAIL>',
        timestamp: '2024-01-15T10:30:00Z',
        severity: 'info'
      },
      {
        id: '2',
        type: 'dataset_upload',
        message: 'Large dataset uploaded: Sales_Data_2024.csv (450MB)',
        timestamp: '2024-01-15T09:45:00Z',
        severity: 'success'
      },
      {
        id: '3',
        type: 'quota_exceeded',
        message: 'User <EMAIL> exceeded storage quota',
        timestamp: '2024-01-15T08:20:00Z',
        severity: 'warning'
      },
      {
        id: '4',
        type: 'system_alert',
        message: 'Database backup completed successfully',
        timestamp: '2024-01-15T06:00:00Z',
        severity: 'success'
      },
      {
        id: '5',
        type: 'system_alert',
        message: 'High CPU usage detected on server-02',
        timestamp: '2024-01-14T23:15:00Z',
        severity: 'error'
      }
    ];

    const mockAlerts: SystemAlert[] = [
      {
        id: '1',
        title: 'Storage Warning',
        message: 'System storage is at 85% capacity. Consider adding more storage.',
        severity: 'warning',
        timestamp: '2024-01-15T08:00:00Z',
        resolved: false
      },
      {
        id: '2',
        title: 'Security Update',
        message: 'New security patches available for the authentication system.',
        severity: 'info',
        timestamp: '2024-01-14T16:30:00Z',
        resolved: false
      },
      {
        id: '3',
        title: 'Backup Failed',
        message: 'Automated backup failed for database cluster-03.',
        severity: 'error',
        timestamp: '2024-01-14T02:00:00Z',
        resolved: true
      }
    ];

    setTimeout(() => {
      setMetrics(mockMetrics);
      setRecentActivity(mockActivity);
      setSystemAlerts(mockAlerts);
      setLoading(false);
    }, 1000);
  }, []);

  const handleToggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  const handleBackToAdmin = () => {
    navigate('/app/admin-dashboard');
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_registration': return <People fontSize="small" />;
      case 'dataset_upload': return <CloudUpload fontSize="small" />;
      case 'system_alert': return <Notifications fontSize="small" />;
      case 'quota_exceeded': return <Warning fontSize="small" />;
      default: return <Info fontSize="small" />;
    }
  };

  const getAlertIcon = (severity: string) => {
    switch (severity) {
      case 'error': return <Error />;
      case 'warning': return <Warning />;
      case 'info': return <Info />;
      default: return <CheckCircle />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      return `${diffInMinutes} minutes ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} hours ago`;
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const activeAlerts = systemAlerts.filter(alert => !alert.resolved);
  const systemHealth = {
    overall: 'Good',
    uptime: 99.9,
    responseTime: 145,
    errorRate: 0.02
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: 'background.default',
        transition: 'all 0.3s ease-in-out',
        ...(isMaximized && {
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 9999,
          bgcolor: 'background.paper'
        })
      }}
    >
      <Container
        maxWidth={isMaximized ? false : 'xl'}
        sx={{
          py: 3,
          px: isMaximized ? 3 : undefined,
          height: isMaximized ? '100vh' : 'auto',
          overflow: isMaximized ? 'auto' : 'visible'
        }}
      >
        <PageTitle
          title="Admin Overview"
          description="System overview and key metrics dashboard"
          icon={<DashboardIcon />}
          breadcrumbs={[
            {
              label: 'Admin Dashboard',
              onClick: () => navigate('/app/admin-dashboard')
            },
            {
              label: 'Overview'
            }
          ]}
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Refresh Data">
                <IconButton
                  onClick={handleRefresh}
                  sx={{
                    bgcolor: 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title={isMaximized ? 'Minimize' : 'Maximize'}>
                <IconButton
                  onClick={handleToggleMaximize}
                  sx={{
                    bgcolor: 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                >
                  {isMaximized ? <FullscreenExit /> : <FullscreenIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          }
        />

        {/* System Alerts */}
        {activeAlerts.length > 0 && (
          <Box sx={{ mb: 3 }}>
            {activeAlerts.map((alert) => (
              <Alert
                key={alert.id}
                severity={alert.severity}
                icon={getAlertIcon(alert.severity)}
                sx={{ mb: 1 }}
              >
                <Box>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    {alert.title}
                  </Typography>
                  <Typography variant="body2">
                    {alert.message}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {formatTimestamp(alert.timestamp)}
                  </Typography>
                </Box>
              </Alert>
            ))}
          </Box>
        )}

        {/* Key Metrics */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          {metrics.map((metric) => (
            <Grid item xs={12} sm={6} md={3} key={metric.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                        {metric.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {metric.title}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        {metric.changeType === 'increase' ? (
                          <TrendingUp fontSize="small" color="success" />
                        ) : (
                          <TrendingDown fontSize="small" color="error" />
                        )}
                        <Typography
                          variant="caption"
                          color={metric.changeType === 'increase' ? 'success.main' : 'error.main'}
                        >
                          {Math.abs(metric.change)}%
                        </Typography>
                      </Box>
                    </Box>
                    <Avatar
                      sx={{
                        bgcolor: metric.color,
                        width: 56,
                        height: 56
                      }}
                    >
                      {metric.icon}
                    </Avatar>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Grid container spacing={3}>
          {/* System Health */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: '100%' }}>
              <CardHeader
                title="System Health"
                avatar={
                  <Avatar sx={{ bgcolor: 'success.main' }}>
                    <AdminPanelSettings />
                  </Avatar>
                }
              />
              <CardContent>
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Overall Status</Typography>
                    <Chip
                      label={systemHealth.overall}
                      color="success"
                      size="small"
                    />
                  </Box>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">Uptime</Typography>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {systemHealth.uptime}%
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={systemHealth.uptime}
                    color="success"
                  />
                </Box>

                <Divider sx={{ my: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Response Time
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {systemHealth.responseTime}ms
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Error Rate
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {systemHealth.errorRate}%
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Recent Activity */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: '100%' }}>
              <CardHeader
                title="Recent Activity"
                avatar={
                  <Avatar sx={{ bgcolor: 'info.main' }}>
                    <Schedule />
                  </Avatar>
                }
              />
              <CardContent sx={{ pt: 0 }}>
                <List sx={{ py: 0 }}>
                  {recentActivity.slice(0, 5).map((activity, index) => (
                    <React.Fragment key={activity.id}>
                      <ListItem sx={{ px: 0 }}>
                        <ListItemIcon sx={{ minWidth: 36 }}>
                          <Avatar
                            sx={{
                              width: 32,
                              height: 32,
                              bgcolor: `${activity.severity}.main`
                            }}
                          >
                            {getActivityIcon(activity.type)}
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {activity.message}
                            </Typography>
                          }
                          secondary={
                            <Typography variant="caption" color="text.secondary">
                              {formatTimestamp(activity.timestamp)}
                            </Typography>
                          }
                        />
                      </ListItem>
                      {index < recentActivity.slice(0, 5).length - 1 && (
                        <Divider variant="inset" component="li" />
                      )}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Quick Actions */}
          <Grid item xs={12}>
            <Card>
              <CardHeader
                title="Quick Actions"
                avatar={
                  <Avatar sx={{ bgcolor: 'primary.main' }}>
                    <Analytics />
                  </Avatar>
                }
              />
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          bgcolor: 'action.hover',
                          transform: 'translateY(-2px)'
                        }
                      }}
                      onClick={() => navigate('/app/admin/users')}
                    >
                      <People sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                      <Typography variant="subtitle2">Manage Users</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          bgcolor: 'action.hover',
                          transform: 'translateY(-2px)'
                        }
                      }}
                      onClick={() => navigate('/app/admin/dataset-quota')}
                    >
                      <Storage sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                      <Typography variant="subtitle2">Dataset Quotas</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          bgcolor: 'action.hover',
                          transform: 'translateY(-2px)'
                        }
                      }}
                      onClick={() => navigate('/app/admin/system-monitoring')}
                    >
                      <Analytics sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
                      <Typography variant="subtitle2">System Monitor</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Paper
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          bgcolor: 'action.hover',
                          transform: 'translateY(-2px)'
                        }
                      }}
                      onClick={() => navigate('/app/admin/settings')}
                    >
                      <AdminPanelSettings sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                      <Typography variant="subtitle2">Admin Settings</Typography>
                    </Paper>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default AdminOverviewPage;