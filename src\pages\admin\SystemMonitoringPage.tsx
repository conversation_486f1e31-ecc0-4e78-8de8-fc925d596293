import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Tooltip,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Button,
  LinearProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar
} from '@mui/material';
import {
  ArrowBack,
  Minimize,
  Maximize,
  Memory,
  Storage,
  Speed,
  NetworkCheck,
  Warning,
  CheckCircle,
  Error,
  Info,
  Refresh,
  TrendingUp,
  TrendingDown,
  Computer,
  Storage as Database,
  Cloud,
  Monitor as MonitorIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import PageTitle from '../../components/UI/PageTitle';

interface SystemMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  status: 'healthy' | 'warning' | 'critical';
  trend: 'up' | 'down' | 'stable';
  lastUpdated: string;
}

interface SystemAlert {
  id: string;
  type: 'error' | 'warning' | 'info';
  message: string;
  timestamp: string;
  resolved: boolean;
}

interface ServiceStatus {
  id: string;
  name: string;
  status: 'online' | 'offline' | 'degraded';
  uptime: string;
  responseTime: number;
  lastCheck: string;
}

const SystemMonitoringPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const [isMaximized, setIsMaximized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState(new Date());

  // Mock data - replace with real API calls
  const [systemMetrics] = useState<SystemMetric[]>([
    {
      id: '1',
      name: 'CPU Usage',
      value: 45,
      unit: '%',
      status: 'healthy',
      trend: 'stable',
      lastUpdated: '2 minutes ago'
    },
    {
      id: '2',
      name: 'Memory Usage',
      value: 78,
      unit: '%',
      status: 'warning',
      trend: 'up',
      lastUpdated: '1 minute ago'
    },
    {
      id: '3',
      name: 'Disk Usage',
      value: 62,
      unit: '%',
      status: 'healthy',
      trend: 'stable',
      lastUpdated: '3 minutes ago'
    },
    {
      id: '4',
      name: 'Network I/O',
      value: 234,
      unit: 'MB/s',
      status: 'healthy',
      trend: 'down',
      lastUpdated: '1 minute ago'
    }
  ]);

  const [systemAlerts] = useState<SystemAlert[]>([
    {
      id: '1',
      type: 'warning',
      message: 'High memory usage detected on server-01',
      timestamp: '5 minutes ago',
      resolved: false
    },
    {
      id: '2',
      type: 'info',
      message: 'Database backup completed successfully',
      timestamp: '1 hour ago',
      resolved: true
    },
    {
      id: '3',
      type: 'error',
      message: 'Failed to connect to external API service',
      timestamp: '2 hours ago',
      resolved: false
    }
  ]);

  const [serviceStatuses] = useState<ServiceStatus[]>([
    {
      id: '1',
      name: 'Web Server',
      status: 'online',
      uptime: '99.9%',
      responseTime: 120,
      lastCheck: '30 seconds ago'
    },
    {
      id: '2',
      name: 'Database',
      status: 'online',
      uptime: '99.8%',
      responseTime: 45,
      lastCheck: '30 seconds ago'
    },
    {
      id: '3',
      name: 'File Storage',
      status: 'degraded',
      uptime: '98.5%',
      responseTime: 890,
      lastCheck: '1 minute ago'
    },
    {
      id: '4',
      name: 'Email Service',
      status: 'online',
      uptime: '99.9%',
      responseTime: 200,
      lastCheck: '45 seconds ago'
    }
  ]);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleToggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  const handleBackToAdmin = () => {
    navigate('/app/admin-dashboard');
  };

  const handleRefresh = () => {
    setIsLoading(true);
    setLastRefresh(new Date());
    // Simulate refresh
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'online':
        return 'success';
      case 'warning':
      case 'degraded':
        return 'warning';
      case 'critical':
      case 'offline':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'online':
        return <CheckCircle color="success" />;
      case 'warning':
      case 'degraded':
        return <Warning color="warning" />;
      case 'critical':
      case 'offline':
        return <Error color="error" />;
      default:
        return <Info />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp color="error" />;
      case 'down':
        return <TrendingDown color="success" />;
      default:
        return null;
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <Error color="error" />;
      case 'warning':
        return <Warning color="warning" />;
      case 'info':
        return <Info color="info" />;
      default:
        return <Info />;
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: 'background.default',
        transition: 'all 0.3s ease-in-out',
        ...(isMaximized && {
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 9999,
          bgcolor: 'background.paper'
        })
      }}
    >
      <Container
        maxWidth={isMaximized ? false : 'xl'}
        sx={{
          py: 3,
          px: isMaximized ? 3 : undefined,
          height: isMaximized ? '100vh' : 'auto',
          overflow: isMaximized ? 'auto' : 'visible'
        }}
      >
        <PageTitle
          title="System Monitoring"
          description="Real-time system performance and health monitoring"
          icon={<MonitorIcon />}
          breadcrumbs={[
             {
               label: 'Admin Dashboard',
               onClick: () => navigate('/app/admin-dashboard')
             },
             {
               label: 'System Monitoring'
             }
           ]}
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Refresh Data">
                <IconButton
                  onClick={handleRefresh}
                  disabled={isLoading}
                  sx={{
                    bgcolor: 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                >
                  <Refresh />
                </IconButton>
              </Tooltip>
              <Tooltip title={isMaximized ? 'Minimize' : 'Maximize'}>
                <IconButton
                  onClick={handleToggleMaximize}
                  sx={{
                    bgcolor: 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                >
                  {isMaximized ? <FullscreenExit /> : <FullscreenIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          }
        />

        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', mb: 3 }}>
          <Typography variant="body2" color="text.secondary">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </Typography>
        </Box>

        {/* System Metrics */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          {systemMetrics.map((metric) => (
            <Grid item xs={12} sm={6} md={3} key={metric.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="h6" component="div">
                      {metric.name}
                    </Typography>
                    {getTrendIcon(metric.trend)}
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h4" component="div" sx={{ mr: 1 }}>
                      {metric.value}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {metric.unit}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={metric.value}
                    color={getStatusColor(metric.status) as any}
                    sx={{ mb: 1 }}
                  />
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Chip
                      size="small"
                      label={metric.status}
                      color={getStatusColor(metric.status) as any}
                      icon={getStatusIcon(metric.status)}
                    />
                    <Typography variant="caption" color="text.secondary">
                      {metric.lastUpdated}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Grid container spacing={3}>
          {/* Service Status */}
          <Grid item xs={12} lg={8}>
            <Card>
              <CardHeader
                title="Service Status"
                avatar={<Computer />}
                action={
                  <Button size="small" onClick={handleRefresh} disabled={isLoading}>
                    Check All
                  </Button>
                }
              />
              <CardContent>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Service</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Uptime</TableCell>
                        <TableCell>Response Time</TableCell>
                        <TableCell>Last Check</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {serviceStatuses.map((service) => (
                        <TableRow key={service.id}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {service.name === 'Database' && <Database />}
                              {service.name === 'File Storage' && <Cloud />}
                              {(service.name === 'Web Server' || service.name === 'Email Service') && <Computer />}
                              {service.name}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              size="small"
                              label={service.status}
                              color={getStatusColor(service.status) as any}
                              icon={getStatusIcon(service.status)}
                            />
                          </TableCell>
                          <TableCell>{service.uptime}</TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {service.responseTime}ms
                              {service.responseTime > 500 && <Warning color="warning" fontSize="small" />}
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" color="text.secondary">
                              {service.lastCheck}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* System Alerts */}
          <Grid item xs={12} lg={4}>
            <Card>
              <CardHeader
                title="System Alerts"
                avatar={<Warning />}
                action={
                  <Chip
                    size="small"
                    label={`${systemAlerts.filter(alert => !alert.resolved).length} Active`}
                    color={systemAlerts.filter(alert => !alert.resolved).length > 0 ? 'warning' : 'success'}
                  />
                }
              />
              <CardContent>
                <List dense>
                  {systemAlerts.slice(0, 5).map((alert) => (
                    <ListItem key={alert.id} sx={{ px: 0 }}>
                      <ListItemIcon>
                        <Avatar sx={{ width: 32, height: 32, bgcolor: 'transparent' }}>
                          {getAlertIcon(alert.type)}
                        </Avatar>
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2" sx={{ flex: 1 }}>
                              {alert.message}
                            </Typography>
                            {alert.resolved && (
                              <Chip size="small" label="Resolved" color="success" />
                            )}
                          </Box>
                        }
                        secondary={
                          <Typography variant="caption" color="text.secondary">
                            {alert.timestamp}
                          </Typography>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
                {systemAlerts.length > 5 && (
                  <Box sx={{ textAlign: 'center', mt: 2 }}>
                    <Button size="small" variant="outlined">
                      View All Alerts
                    </Button>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* System Information */}
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardHeader title="Server Information" avatar={<Computer />} />
              <CardContent>
                <List dense>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText primary="OS" secondary="Ubuntu 20.04 LTS" />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText primary="Kernel" secondary="5.4.0-74-generic" />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText primary="Architecture" secondary="x86_64" />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText primary="Uptime" secondary="15 days, 4 hours" />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardHeader title="Hardware Resources" avatar={<Memory />} />
              <CardContent>
                <List dense>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText primary="CPU" secondary="Intel Xeon E5-2686 v4 (8 cores)" />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText primary="Memory" secondary="32 GB DDR4" />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText primary="Storage" secondary="1 TB NVMe SSD" />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText primary="Network" secondary="10 Gbps" />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardHeader title="Application Info" avatar={<Speed />} />
              <CardContent>
                <List dense>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText primary="Version" secondary="DataStatPro v2.1.0" />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText primary="Build" secondary="#2024.01.15-prod" />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText primary="Environment" secondary="Production" />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText primary="Last Deploy" secondary="2 days ago" />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default SystemMonitoringPage;