# Email-Verified Guest Access System - Technical Architecture Document

## 1. Architecture Design

```mermaid
graph TD
    A[User Browser] --> B[React Frontend Application]
    B --> C[Enhanced AuthContext]
    C --> D[Supabase Client SDK]
    D --> E[Supabase Database]
    D --> F[Supabase Edge Functions]
    F --> G[Email Service Provider]
    
    subgraph "Frontend Layer"
        B
        C
        H[EmailVerifiedGuestAccess Component]
        I[GuestAnalytics Tracker]
        J[Privacy Controls]
    end
    
    subgraph "Backend Services (Supabase)"
        E
        F
        K[Auth Service]
        L[Real-time Subscriptions]
    end
    
    subgraph "External Services"
        G
        M[Analytics Service]
    end
    
    B --> H
    B --> I
    B --> J
    C --> K
    E --> L
    I --> M
```

## 2. Technology Description

- **Frontend**: React@18 + TypeScript@5 + Material-UI@5 + Vite@5
- **Backend**: Supabase (PostgreSQL + Edge Functions + Auth + Real-time)
- **Email Service**: Supabase Auth Email Templates + Custom Edge Functions
- **Analytics**: Custom tracking with Supabase database storage
- **State Management**: React Context API + Local Storage
- **Validation**: Zod for schema validation
- **Testing**: Vitest + React Testing Library

## 3. Route Definitions

| Route | Purpose | Access Level |
|-------|---------|-------------|
| `/auth` | Main authentication container with tabs | Public |
| `/auth?tab=verified-guest` | Email-verified guest registration/login | Public |
| `/auth/verify-email?token=xxx` | Email verification handler | Public |
| `/guest-dashboard` | Verified guest dashboard with analytics | Verified Guest |
| `/guest-settings` | Guest privacy and preference settings | Verified Guest |
| `/guest-migration` | Legacy guest migration flow | Legacy Guest |

## 4. API Definitions

### 4.1 Core Guest Management APIs

#### Guest User Registration
```
POST /api/guest/register
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| email | string | true | Valid email address for guest registration |
| marketingConsent | boolean | false | Consent for marketing communications |
| privacyConsent | boolean | true | Consent for privacy policy |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| success | boolean | Registration success status |
| guestUserId | string | UUID of created guest user |
| message | string | Success or error message |

Example Request:
```json
{
  "email": "<EMAIL>",
  "marketingConsent": false,
  "privacyConsent": true
}
```

Example Response:
```json
{
  "success": true,
  "guestUserId": "123e4567-e89b-12d3-a456-************",
  "message": "Verification email sent successfully"
}
```

#### Email Verification
```
POST /api/guest/verify-email
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| token | string | true | Email verification token |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| success | boolean | Verification success status |
| guestUser | GuestUser | Guest user object if successful |
| message | string | Success or error message |

#### Guest User Login
```
POST /api/guest/login
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| email | string | true | Verified email address |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| success | boolean | Login success status |
| guestUser | GuestUser | Guest user object |
| sessionToken | string | Session token for tracking |

### 4.2 Analytics and Tracking APIs

#### Start Analytics Session
```
POST /api/guest/analytics/session/start
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| guestUserId | string | true | Guest user UUID |
| sessionId | string | true | Unique session identifier |
| userAgent | string | false | Browser user agent |
| ipAddress | string | false | User IP address (anonymized) |

#### Track Feature Usage
```
POST /api/guest/analytics/feature-usage
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| guestUserId | string | true | Guest user UUID |
| sessionId | string | true | Session identifier |
| feature | string | true | Feature name used |
| metadata | object | false | Additional feature usage data |

#### End Analytics Session
```
POST /api/guest/analytics/session/end
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| guestUserId | string | true | Guest user UUID |
| sessionId | string | true | Session identifier |
| duration | number | true | Session duration in seconds |

### 4.3 Privacy and Preferences APIs

#### Update Guest Preferences
```
PUT /api/guest/preferences
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| guestUserId | string | true | Guest user UUID |
| preferences | object | true | Preference settings object |

Example Request:
```json
{
  "guestUserId": "123e4567-e89b-12d3-a456-************",
  "preferences": {
    "theme": "dark",
    "language": "en",
    "analyticsConsent": true,
    "marketingConsent": false,
    "dataRetentionPeriod": "90days"
  }
}
```

#### Export Guest Data (GDPR)
```
GET /api/guest/data-export
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| guestUserId | string | true | Guest user UUID |
| email | string | true | Email for verification |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| success | boolean | Export success status |
| data | object | Complete guest user data |
| exportDate | string | ISO date of export |

#### Delete Guest Account (GDPR)
```
DELETE /api/guest/account
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| guestUserId | string | true | Guest user UUID |
| email | string | true | Email for verification |
| confirmationToken | string | true | Email confirmation token |

## 5. Server Architecture Diagram

```mermaid
graph TD
    A[Client / Frontend] --> B[API Gateway Layer]
    B --> C[Authentication Middleware]
    C --> D[Guest Service Layer]
    D --> E[Analytics Service Layer]
    D --> F[Privacy Service Layer]
    D --> G[Email Service Layer]
    
    E --> H[Database Repository Layer]
    F --> H
    G --> I[External Email Provider]
    H --> J[(Supabase PostgreSQL)]
    
    subgraph "Supabase Edge Functions"
        B
        C
        D
        E
        F
        G
    end
    
    subgraph "Data Layer"
        H
        J
    end
    
    subgraph "External Services"
        I
    end
```

## 6. Data Model

### 6.1 Data Model Definition

```mermaid
erDiagram
    GUEST_USERS ||--o{ GUEST_ANALYTICS : tracks
    GUEST_USERS ||--o{ STATISTICAL_METHOD_ANALYTICS : uses
    GUEST_USERS ||--o{ ANALYSIS_WORKFLOWS : creates
    
    GUEST_USERS {
        uuid id PK
        varchar email UK
        boolean email_verified
        varchar verification_token
        timestamp verification_expires_at
        timestamp last_login_at
        integer total_sessions
        integer total_session_duration
        jsonb preferences
        boolean marketing_consent
        timestamp created_at
        timestamp updated_at
    }
    
    GUEST_ANALYTICS {
        uuid id PK
        uuid guest_user_id FK
        varchar session_id
        timestamp session_start
        timestamp session_end
        integer session_duration
        jsonb features_used
        jsonb pages_visited
        jsonb analysis_methods_used
        inet ip_address
        text user_agent
        timestamp created_at
    }
    
    STATISTICAL_METHOD_ANALYTICS {
        uuid id PK
        uuid guest_user_id FK
        varchar method_name
        varchar access_level
        jsonb parameters_used
        timestamp created_at
    }
    
    ANALYSIS_WORKFLOWS {
        uuid id PK
        uuid guest_user_id FK
        varchar workflow_name
        jsonb workflow_steps
        varchar status
        timestamp created_at
        timestamp updated_at
    }
```

### 6.2 Data Definition Language

#### Guest Users Table
```sql
-- Create guest_users table with comprehensive tracking
CREATE TABLE IF NOT EXISTS public.guest_users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    verification_expires_at TIMESTAMP WITH TIME ZONE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    total_sessions INTEGER DEFAULT 0,
    total_session_duration INTEGER DEFAULT 0, -- in seconds
    preferences JSONB DEFAULT '{}',
    marketing_consent BOOLEAN DEFAULT FALSE,
    privacy_consent BOOLEAN DEFAULT TRUE,
    data_retention_period VARCHAR(20) DEFAULT '90days',
    migrated_from_anonymous BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_retention_period CHECK (data_retention_period IN ('30days', '90days', '1year', '2years'))
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_guest_users_email ON public.guest_users(email);
CREATE INDEX IF NOT EXISTS idx_guest_users_verification_token ON public.guest_users(verification_token) WHERE verification_token IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_guest_users_email_verified ON public.guest_users(email_verified);
CREATE INDEX IF NOT EXISTS idx_guest_users_last_login ON public.guest_users(last_login_at);

-- Initial data for testing
INSERT INTO public.guest_users (email, email_verified, preferences, marketing_consent) VALUES
('<EMAIL>', true, '{"theme": "light", "language": "en"}', false),
('<EMAIL>', true, '{"theme": "dark", "language": "en", "analyticsConsent": true}', true)
ON CONFLICT (email) DO NOTHING;
```

#### Guest Analytics Table
```sql
-- Create guest_analytics table for detailed usage tracking
CREATE TABLE IF NOT EXISTS public.guest_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    guest_user_id UUID REFERENCES public.guest_users(id) ON DELETE CASCADE,
    session_id VARCHAR(100) NOT NULL,
    session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    session_end TIMESTAMP WITH TIME ZONE,
    session_duration INTEGER, -- calculated duration in seconds
    features_used JSONB DEFAULT '[]',
    pages_visited JSONB DEFAULT '[]',
    analysis_methods_used JSONB DEFAULT '[]',
    ip_address INET, -- anonymized IP for analytics
    user_agent TEXT,
    referrer_url TEXT,
    device_type VARCHAR(50), -- mobile, tablet, desktop
    browser_name VARCHAR(50),
    os_name VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for analytics queries
CREATE INDEX IF NOT EXISTS idx_guest_analytics_guest_user_id ON public.guest_analytics(guest_user_id);
CREATE INDEX IF NOT EXISTS idx_guest_analytics_session_id ON public.guest_analytics(session_id);
CREATE INDEX IF NOT EXISTS idx_guest_analytics_session_start ON public.guest_analytics(session_start DESC);
CREATE INDEX IF NOT EXISTS idx_guest_analytics_created_at ON public.guest_analytics(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_guest_analytics_device_type ON public.guest_analytics(device_type);

-- Sample analytics data
INSERT INTO public.guest_analytics (
    guest_user_id, 
    session_id, 
    session_start, 
    session_end, 
    session_duration,
    features_used, 
    pages_visited,
    device_type,
    browser_name
) 
SELECT 
    gu.id,
    'session_' || generate_random_uuid()::text,
    NOW() - INTERVAL '2 hours',
    NOW() - INTERVAL '1 hour',
    3600,
    '["data_upload", "statistical_analysis", "chart_generation"]'::jsonb,
    '["/dashboard", "/analysis", "/charts"]'::jsonb,
    'desktop',
    'Chrome'
FROM public.guest_users gu 
WHERE gu.email_verified = true
LIMIT 2;
```

#### Enhanced Statistical Method Analytics
```sql
-- Add guest_user_id to existing statistical_method_analytics table
ALTER TABLE public.statistical_method_analytics 
ADD COLUMN IF NOT EXISTS guest_user_id UUID REFERENCES public.guest_users(id) ON DELETE SET NULL;

-- Create index for guest analytics
CREATE INDEX IF NOT EXISTS idx_statistical_method_analytics_guest_user_id 
ON public.statistical_method_analytics(guest_user_id) 
WHERE guest_user_id IS NOT NULL;

-- Update existing records to support guest tracking
UPDATE public.statistical_method_analytics 
SET access_level = 'verified_guest' 
WHERE access_level = 'guest' AND guest_user_id IS NOT NULL;
```

#### Guest Migration Tracking
```sql
-- Create table to track guest migration from anonymous to verified
CREATE TABLE IF NOT EXISTS public.guest_migration_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id VARCHAR(100),
    migration_type VARCHAR(50) CHECK (migration_type IN ('prompted', 'completed', 'skipped', 'abandoned')),
    source_type VARCHAR(50) DEFAULT 'anonymous_guest',
    target_type VARCHAR(50) DEFAULT 'verified_guest',
    guest_user_id UUID REFERENCES public.guest_users(id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index for migration analytics
CREATE INDEX IF NOT EXISTS idx_guest_migration_log_type ON public.guest_migration_log(migration_type);
CREATE INDEX IF NOT EXISTS idx_guest_migration_log_created_at ON public.guest_migration_log(created_at DESC);
```

#### Database Functions for Guest Management
```sql
-- Function to create guest user with validation
CREATE OR REPLACE FUNCTION public.create_guest_user(
    p_email VARCHAR(255),
    p_marketing_consent BOOLEAN DEFAULT FALSE
) RETURNS TABLE(
    guest_id UUID,
    verification_token VARCHAR(255),
    success BOOLEAN,
    message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_guest_id UUID;
    v_token VARCHAR(255);
    v_existing_user UUID;
BEGIN
    -- Check if email already exists
    SELECT id INTO v_existing_user 
    FROM public.guest_users 
    WHERE email = p_email;
    
    IF v_existing_user IS NOT NULL THEN
        RETURN QUERY SELECT 
            v_existing_user,
            NULL::VARCHAR(255),
            FALSE,
            'Email already registered'::TEXT;
        RETURN;
    END IF;
    
    -- Generate verification token
    v_token := encode(gen_random_bytes(32), 'hex');
    
    -- Insert guest user
    INSERT INTO public.guest_users (
        email, 
        verification_token, 
        verification_expires_at,
        marketing_consent
    ) VALUES (
        p_email, 
        v_token, 
        NOW() + INTERVAL '24 hours',
        p_marketing_consent
    ) RETURNING id INTO v_guest_id;
    
    RETURN QUERY SELECT 
        v_guest_id,
        v_token,
        TRUE,
        'Guest user created successfully'::TEXT;
        
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT 
            NULL::UUID,
            NULL::VARCHAR(255),
            FALSE,
            SQLERRM::TEXT;
END;
$$;

-- Function to verify guest email
CREATE OR REPLACE FUNCTION public.verify_guest_email(
    p_token VARCHAR(255)
) RETURNS TABLE(
    guest_id UUID,
    email VARCHAR(255),
    success BOOLEAN,
    message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_guest_record RECORD;
BEGIN
    -- Find and verify the token
    UPDATE public.guest_users 
    SET 
        email_verified = TRUE,
        verification_token = NULL,
        verification_expires_at = NULL,
        updated_at = NOW()
    WHERE 
        verification_token = p_token 
        AND verification_expires_at > NOW()
        AND email_verified = FALSE
    RETURNING id, email INTO v_guest_record;
    
    IF v_guest_record.id IS NOT NULL THEN
        RETURN QUERY SELECT 
            v_guest_record.id,
            v_guest_record.email,
            TRUE,
            'Email verified successfully'::TEXT;
    ELSE
        RETURN QUERY SELECT 
            NULL::UUID,
            NULL::VARCHAR(255),
            FALSE,
            'Invalid or expired verification token'::TEXT;
    END IF;
END;
$$;

-- Function to track guest analytics session
CREATE OR REPLACE FUNCTION public.start_guest_analytics_session(
    p_guest_user_id UUID,
    p_session_id VARCHAR(100),
    p_user_agent TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_analytics_id UUID;
BEGIN
    INSERT INTO public.guest_analytics (
        guest_user_id,
        session_id,
        user_agent,
        ip_address
    ) VALUES (
        p_guest_user_id,
        p_session_id,
        p_user_agent,
        p_ip_address
    ) RETURNING id INTO v_analytics_id;
    
    RETURN v_analytics_id;
END;
$$;

-- Function to end guest analytics session
CREATE OR REPLACE FUNCTION public.end_guest_analytics_session(
    p_session_id VARCHAR(100),
    p_features_used JSONB DEFAULT '[]',
    p_pages_visited JSONB DEFAULT '[]'
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.guest_analytics 
    SET 
        session_end = NOW(),
        session_duration = EXTRACT(EPOCH FROM (NOW() - session_start))::INTEGER,
        features_used = p_features_used,
        pages_visited = p_pages_visited
    WHERE session_id = p_session_id AND session_end IS NULL;
    
    RETURN FOUND;
END;
$$;
```

#### Row Level Security (RLS) Policies
```sql
-- Enable RLS on guest tables
ALTER TABLE public.guest_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.guest_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.guest_migration_log ENABLE ROW LEVEL SECURITY;

-- Policies for guest_users table
CREATE POLICY "Guest users can view their own data" ON public.guest_users
    FOR SELECT USING (true); -- Allow system-level access for verification

CREATE POLICY "System can manage guest users" ON public.guest_users
    FOR ALL USING (true); -- Full system access for backend functions

-- Policies for guest_analytics table
CREATE POLICY "System can manage guest analytics" ON public.guest_analytics
    FOR ALL USING (true); -- System-level access for analytics

-- Policies for guest_migration_log table
CREATE POLICY "System can manage migration log" ON public.guest_migration_log
    FOR ALL USING (true); -- System-level access for migration tracking

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.guest_users TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.guest_analytics TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.guest_migration_log TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
```

## 7. TypeScript Type Definitions

```typescript
// Core guest user types
export interface GuestUser {
  id: string;
  email: string;
  emailVerified: boolean;
  lastLoginAt: Date | null;
  totalSessions: number;
  totalSessionDuration: number; // in seconds
  preferences: GuestPreferences;
  marketingConsent: boolean;
  privacyConsent: boolean;
  dataRetentionPeriod: DataRetentionPeriod;
  migratedFromAnonymous: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface GuestPreferences {
  theme?: 'light' | 'dark' | 'auto';
  language?: string;
  analyticsConsent?: boolean;
  marketingConsent?: boolean;
  dataRetentionPeriod?: DataRetentionPeriod;
  allowCookies?: boolean;
  timezone?: string;
  dateFormat?: string;
  numberFormat?: string;
}

export type DataRetentionPeriod = '30days' | '90days' | '1year' | '2years';

// Analytics types
export interface GuestAnalyticsSession {
  id: string;
  guestUserId: string;
  sessionId: string;
  sessionStart: Date;
  sessionEnd: Date | null;
  sessionDuration: number | null;
  featuresUsed: string[];
  pagesVisited: string[];
  analysisMethodsUsed: string[];
  ipAddress: string | null;
  userAgent: string | null;
  deviceType: DeviceType | null;
  browserName: string | null;
  osName: string | null;
  createdAt: Date;
}

export type DeviceType = 'mobile' | 'tablet' | 'desktop';

// API response types
export interface GuestRegistrationResponse {
  success: boolean;
  guestUserId?: string;
  message: string;
  error?: string;
}

export interface GuestVerificationResponse {
  success: boolean;
  guestUser?: GuestUser;
  message: string;
  error?: string;
}

export interface GuestLoginResponse {
  success: boolean;
  guestUser?: GuestUser;
  sessionToken?: string;
  message: string;
  error?: string;
}

// Migration types
export interface GuestMigrationLog {
  id: string;
  sessionId: string;
  migrationType: MigrationType;
  sourceType: string;
  targetType: string;
  guestUserId: string | null;
  metadata: Record<string, any>;
  createdAt: Date;
}

export type MigrationType = 'prompted' | 'completed' | 'skipped' | 'abandoned';

// Privacy and GDPR types
export interface GuestDataExport {
  guestUser: GuestUser;
  analyticsData: GuestAnalyticsSession[];
  statisticalMethodUsage: StatisticalMethodAnalytics[];
  migrationHistory: GuestMigrationLog[];
  exportDate: Date;
  dataRetentionInfo: {
    retentionPeriod: DataRetentionPeriod;
    nextCleanupDate: Date;
  };
}

export interface PrivacySettings {
  analyticsConsent: boolean;
  marketingConsent: boolean;
  dataRetentionPeriod: DataRetentionPeriod;
  allowCookies: boolean;
  allowPersonalization: boolean;
}

// Enhanced auth context types
export interface EnhancedAuthContextType extends AuthContextType {
  // Guest-specific properties
  guestUser: GuestUser | null;
  isVerifiedGuest: boolean;
  isLegacyGuest: boolean;
  guestSessionId: string | null;
  
  // Guest management methods
  registerGuestUser: (email: string, marketingConsent?: boolean) => Promise<GuestRegistrationResponse>;
  verifyGuestEmail: (token: string) => Promise<GuestVerificationResponse>;
  loginAsVerifiedGuest: (email: string) => Promise<GuestLoginResponse>;
  updateGuestPreferences: (preferences: Partial<GuestPreferences>) => Promise<void>;
  exportGuestData: () => Promise<GuestDataExport>;
  deleteGuestAccount: (confirmationToken: string) => Promise<{ success: boolean; message: string }>;
  
  // Analytics methods
  startAnalyticsSession: () => Promise<string>; // returns session ID
  trackFeatureUsage: (feature: string, metadata?: Record<string, any>) => Promise<void>;
  trackPageVisit: (page: string) => Promise<void>;
  endAnalyticsSession: () => Promise<void>;
  
  // Migration methods
  promptGuestMigration: () => void;
  completeMigration: (email: string) => Promise<void>;
  skipMigration: () => void;
}
```

This technical architecture document provides the detailed system design, API specifications, and data models needed to implement the Email-Verified Guest Access system in DataStatPro. The architecture maintains compatibility with the existing system while adding comprehensive tracking and privacy-compliant features for verified guest users.