// Simple test component to verify router functionality

import React from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';
import { routeRegistry, getNavigationRoutes, getRoutesByCategory } from '../routeConfig';

interface RouterTestProps {
  onNavigate: (path: string) => void;
}

export const RouterTest: React.FC<RouterTestProps> = ({ onNavigate }) => {
  const allRoutes = routeRegistry.getAllRoutes();
  const navigationRoutes = getNavigationRoutes();
  const routesByCategory = getRoutesByCategory();

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        🚀 Router System Test
      </Typography>
      
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Router Statistics
        </Typography>
        <Typography>Total Routes Registered: {allRoutes.length}</Typography>
        <Typography>Navigation Routes: {navigationRoutes.length}</Typography>
        <Typography>Categories: {Object.keys(routesByCategory).length}</Typography>
      </Paper>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Routes by Category
        </Typography>
        {Object.entries(routesByCategory).map(([category, routes]) => (
          <Box key={category} sx={{ mb: 2 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold', textTransform: 'capitalize' }}>
              {category} ({routes.length} routes)
            </Typography>
            <Box sx={{ ml: 2 }}>
              {routes.slice(0, 5).map((route) => (
                <Box key={route.path} sx={{ mb: 1 }}>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => onNavigate(route.path)}
                    sx={{ mr: 1, mb: 1 }}
                  >
                    {route.metadata?.title || route.path}
                  </Button>
                  <Typography variant="caption" color="text.secondary">
                    {route.metadata?.description}
                  </Typography>
                </Box>
              ))}
              {routes.length > 5 && (
                <Typography variant="caption" color="text.secondary">
                  ... and {routes.length - 5} more
                </Typography>
              )}
            </Box>
          </Box>
        ))}
      </Paper>

      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Quick Navigation Test
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          <Button variant="contained" onClick={() => onNavigate('/app/dashboard')}>
            Dashboard
          </Button>
          <Button variant="contained" onClick={() => onNavigate('data-management')}>
            Data Management
          </Button>
          <Button variant="contained" onClick={() => onNavigate('stats')}>
            Statistics
          </Button>
          <Button variant="contained" onClick={() => onNavigate('charts')}>
            Charts
          </Button>
          <Button variant="contained" onClick={() => onNavigate('correlation-analysis')}>
            Correlation
          </Button>
          <Button variant="contained" onClick={() => onNavigate('assistant')}>
            Assistant
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default RouterTest;
