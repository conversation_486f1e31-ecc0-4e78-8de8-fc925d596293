import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  IconButton,
  Box,
  useTheme,
  useMediaQuery,
  Tooltip,
  Menu,
  MenuItem,
  Avatar,
  Badge,
  alpha,
  SwipeableDrawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  CircularProgress
} from '@mui/material';
import {
  BarChart as BarChartIcon,
  Assessment as AssessmentIcon,
  B<PERSON>bleChart as BubbleChartIcon,
  TableChart as TableChartIcon,
  Menu as MenuIcon,
  GitHub as GitHubIcon,
  Dashboard as DashboardIcon,
  Notifications as NotificationsIcon,
  Brightness4 as Brightness4Icon,
  Brightness7 as Brightness7Icon,
  HelpOutline as HelpOutlineIcon,
  Person as PersonIcon,
  Settings as SettingsIcon,
  ExitToApp as ExitToAppIcon,
  Inbox as InboxIcon,
  MoreVert as MoreVertIcon,
  Storage as StorageIcon,
  Functions as FunctionsIcon,
  ShowChart as ShowChartIcon,
  Save as SaveIcon,
  Home as HomeIcon,
  Lightbulb as LightbulbIcon,
  Email as EmailIcon,
  Facebook as FacebookIcon, // Import social icons
  Twitter as TwitterIcon,
  YouTube as YouTubeIcon,
  LinkedIn as LinkedInIcon,
  Password as PasswordIcon, // Import Password icon
  Share as ShareIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { useNotifications } from '../../hooks/useNotifications';
import { useAppTheme } from '../../context/ThemeContext';
import { UpdateButton } from '../PWA';
import PasswordChangeModal from '../Auth/PasswordChangeModal'; // Import the modal
import NotificationRichText from '../UI/NotificationRichText';
import SocialShare from '../UI/SocialShare';

interface AppHeaderProps {
  title: string;
  onMenuClick: () => void;
  onNavigate: (path: string) => void; // Add onNavigate
  // onAuthClick and onProfileClick are now handled by onNavigate
}

const AuthAppHeader: React.FC<AppHeaderProps> = ({ title, onMenuClick, onNavigate }) => {
  const { user, signOut } = useAuth();
  const { notifications, unreadCount, loading: notificationsLoading, markAsRead, markAllAsRead } = useNotifications();
  const { themeMode, setThemeMode, appliedTheme } = useAppTheme();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('md'));
  
  const [notificationsAnchorEl, setNotificationsAnchorEl] = useState<null | HTMLElement>(null);
  const [userAnchorEl, setUserAnchorEl] = useState<null | HTMLElement>(null);
  const [moreMenuAnchorEl, setMoreMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [quickMenuOpen, setQuickMenuOpen] = useState(false);
  const [passwordModalOpen, setPasswordModalOpen] = useState(false); // State for password modal
  const [shareMenuAnchorEl, setShareMenuAnchorEl] = useState<null | HTMLElement>(null);
  
  const handleNotificationsClick = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationsAnchorEl(event.currentTarget);
  };
  
  const handleNotificationsClose = () => {
    setNotificationsAnchorEl(null);
  };
  
  const handleUserMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setUserAnchorEl(event.currentTarget);
  };
  
  const handleUserMenuClose = () => {
    setUserAnchorEl(null);
  };
  
  const handleSettingsClick = () => {
    handleUserMenuClose();
    onNavigate('settings'); 
  };

  const handlePasswordModalOpen = () => {
    handleUserMenuClose(); // Close user menu first
    setPasswordModalOpen(true); // Open password modal
  };

  const handlePasswordModalClose = () => {
    setPasswordModalOpen(false);
  };
  
  const handleMoreMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setMoreMenuAnchorEl(event.currentTarget);
  };
  
  const handleMoreMenuClose = () => {
    setMoreMenuAnchorEl(null);
  };
  
  const handleShareMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setShareMenuAnchorEl(event.currentTarget);
  };
  
  const handleShareMenuClose = () => {
    setShareMenuAnchorEl(null);
  };
  
  const toggleQuickMenu = (open: boolean) => () => {
    setQuickMenuOpen(open);
  };

  const handleProfileClick = () => {
    handleUserMenuClose();
    onNavigate('profile'); // Use onNavigate
  };
  
  // handleAuthClick is removed as onNavigate('auth/login') or similar will be used directly
  
  const handleSignOut = async () => {
    handleUserMenuClose();
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Define navigation items for the more menu and quick menu
  const navItems = [
    { name: 'Home', icon: <HomeIcon />, path: '/home' },
    { name: 'Data Management', icon: <StorageIcon />, path: '/data-management/import' },
    { name: 'Descriptive Statistics', icon: <FunctionsIcon />, path: '/stats/descriptives' },
    { name: 'Inferential Statistics', icon: <AssessmentIcon />, path: '/inferential-stats/ttest' },
    { name: 'Data Visualization', icon: <ShowChartIcon />, path: '/charts' },
    { name: 'Advanced Analysis', icon: <SaveIcon />, path: '/advanced-analysis' },
    { name: 'Help & Resources', icon: <HelpOutlineIcon />, path: '/knowledge-base' },
  ];
  
  // Handle navigation click
  const handleNavClick = (path: string) => {
    // Path should be passed directly to onNavigate without modification
    // Remove the hash prefix if it exists
    const internalPath = path.replace(/^\/#\//, '');
    onNavigate(internalPath);
    handleMoreMenuClose();
    setQuickMenuOpen(false);
  };

  // Theme toggle handler
  const handleThemeToggle = () => {
    const nextTheme = appliedTheme === 'light' ? 'dark' : 'light';
    setThemeMode(nextTheme);
  };

  return (
    <AppBar 
      position="fixed" 
      sx={{ 
        zIndex: theme.zIndex.drawer + 1,
        boxShadow: '0 2px 10px rgba(0,0,0,0.08)',
        background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
      }}
      elevation={0}
    >
      <Toolbar sx={{ 
        minHeight: { xs: 56, sm: 64 },
        px: { xs: 1, sm: 2 }
      }}>
        <IconButton
          color="inherit"
          aria-label="open drawer"
          edge="start"
          onClick={onMenuClick}
          sx={{ mr: 1 }}
          size={isMobile ? "small" : "medium"}
        >
          <MenuIcon />
        </IconButton>
        
        {/* Wrap Logo and Title in a clickable Box */}
        <Box
          onClick={() => onNavigate('/app/dashboard')} // Navigate to dashboard on click
          sx={{
            display: 'flex',
            alignItems: 'center',
            mr: 1,
            cursor: 'pointer', // Indicate clickability
            textDecoration: 'none', // Remove potential underline if wrapped in link later
            color: 'inherit' // Inherit color
          }}
        >
          <Box 
            component="img"
            src="/logo.png"
            alt="DataStatPro Logo"
            sx={{ 
              height: { xs: 28, sm: 32 },
              width: { xs: 28, sm: 32 },
              mr: 1.5,
              borderRadius: '8px',
              backgroundColor: 'white',
              padding: '4px'
            }}
          />
          <Typography 
            variant={isMobile ? "subtitle1" : "h6"} 
            noWrap 
            component="div" 
            sx={{ 
              fontWeight: 'bold',
              letterSpacing: '0.5px'
            }}
          >
            {title}
          </Typography>
        </Box>

        <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Typography 
            variant="body2" 
            sx={{ 
              display: { xs: 'none', md: 'block' },
              color: 'white',
              opacity: 0.9,
              '&:hover': { opacity: 1 }
            }}
          >
            <EmailIcon sx={{ mr: 0.5, verticalAlign: 'middle', fontSize: 'inherit' }} /> <EMAIL>
          </Typography>
        </Box>  {/* Removed the erroneous closing comment tag */}

        {/* Empty Box to allow flexGrow */}
        <Box sx={{ flexGrow: 1 }} /> 

        {/* Desktop Navigation & Actions */}
        <Box sx={{ 
          display: { xs: 'none', md: 'flex' }, 
          alignItems: 'center',
          '& .MuiIconButton-root': {
            mx: { xs: 0.5, sm: 0.75 },
            p: { xs: 0.75, sm: 1 }
          }
        }}>
          <Tooltip title="Data Management">
            <IconButton 
              color="inherit" 
              size="medium"
              onClick={() => handleNavClick('/data-management/import')}
            >
              <StorageIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Descriptive Statistics">
            <IconButton 
              color="inherit"
              size="medium"
              onClick={() => handleNavClick('/stats/descriptives')}
            >
              <FunctionsIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Inferential Statistics">
            <IconButton 
              color="inherit"
              size="medium"
              onClick={() => handleNavClick('/inferential-stats/ttest')}
            >
              <AssessmentIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Data Visualization">
            <IconButton 
              color="inherit"
              size="medium"
              onClick={() => handleNavClick('/charts')}
            >
              <ShowChartIcon />
            </IconButton>
          </Tooltip>

          {/* Social Icons */}
          <Box sx={{ ml: 2, borderLeft: 1, borderColor: 'divider', pl: 1 }}>
            <Tooltip title="Email">
              <IconButton color="inherit" component="a" href="mailto:<EMAIL>" aria-label="Email">
                <EmailIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Facebook">
              <IconButton color="inherit" component="a" href="https://www.facebook.com/datastatpro/" target="_blank" rel="noopener noreferrer" aria-label="Facebook">
                <FacebookIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Twitter">
              <IconButton color="inherit" component="a" href="https://x.com/datastatpro" target="_blank" rel="noopener noreferrer" aria-label="Twitter">
                <TwitterIcon />
              </IconButton>
            </Tooltip>
             <Tooltip title="YouTube">
              <IconButton color="inherit" component="a" href="https://www.youtube.com/watch?v=HF-gWGcZPBo&list=PLHQA3dbkjl7vusT1AceUmJGLQD2vlE42v" target="_blank" rel="noopener noreferrer" aria-label="YouTube">
                <YouTubeIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="LinkedIn">
              <IconButton color="inherit" component="a" href="https://www.linkedin.com/in/nadeem-shafique-butt-99a41418/" target="_blank" rel="noopener noreferrer" aria-label="LinkedIn">
                <LinkedInIcon />
              </IconButton>
            </Tooltip>
          </Box>
          
          {/* Share Button */}
          <Tooltip title="Share this page">
            <IconButton
              color="inherit"
              onClick={handleShareMenuClick}
              size="medium"
              sx={{ ml: 1 }}
            >
              <ShareIcon />
            </IconButton>
          </Tooltip>
          
          {/* Notifications */}
          {/* PWA Update Button */}
          <UpdateButton variant="menu" size="medium" />

          <Tooltip title="Notifications">
            <IconButton
              color="inherit"
              onClick={handleNotificationsClick}
              size="medium"
            >
              <Badge badgeContent={unreadCount} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>

          {/* Theme Toggle */}
          <Tooltip title={`Switch to ${appliedTheme === 'light' ? 'dark' : 'light'} theme`}>
            <IconButton
              color="inherit"
              onClick={handleThemeToggle}
              sx={{ mr: 1 }}
            >
              {appliedTheme === 'light' ? <Brightness4Icon /> : <Brightness7Icon />}
            </IconButton>
          </Tooltip>

          {/* User Authentication */}
          {user ? (
            <Tooltip title="Account">
              <IconButton 
                onClick={handleUserMenuClick}
                size="large"
                edge="end"
                color="inherit"
                sx={{ ml: 1 }}
              >
                <Avatar sx={{ width: 32, height: 32, bgcolor: theme.palette.secondary.main }}>
                  {user.email ? user.email.charAt(0).toUpperCase() : <PersonIcon fontSize="small" />}
                </Avatar>
              </IconButton>
            </Tooltip>
          ) : (
            <Button 
              color="inherit" 
              onClick={() => onNavigate('auth/login')} // Use onNavigate
              startIcon={<PersonIcon />}
              sx={{ ml: 1 }}
            >
              Sign In
            </Button>
          )}
        </Box>

        {/* Mobile Navigation */}
        <Box sx={{ display: { xs: 'flex', md: 'none' } }}>
          <IconButton
            color="inherit"
            onClick={handleMoreMenuClick}
            size="medium"
          >
            <MoreVertIcon />
          </IconButton>
        </Box>
        
        {/* Notifications Menu */}
        <Menu
          anchorEl={notificationsAnchorEl}
          open={Boolean(notificationsAnchorEl)}
          onClose={handleNotificationsClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          slotProps={{
            paper: {
              sx: {
                minWidth: 350,
                maxWidth: 500,
                maxHeight: 400,
                width: 'auto'
              }
            }
          }}
        >
          {notificationsLoading ? (
            <MenuItem>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CircularProgress size={16} />
                <Typography variant="body2">Loading notifications...</Typography>
              </Box>
            </MenuItem>
          ) : notifications.length === 0 ? (
            <MenuItem>
              <ListItemText
                primary="No notifications"
                secondary="You're all caught up!"
              />
            </MenuItem>
          ) : (
            <>
              {notifications.slice(0, 5).map((notification) => (
                <MenuItem
                  key={notification.id}
                  onClick={() => {
                    if (!notification.is_read) {
                      markAsRead(notification.id);
                    }
                  }}
                  sx={{
                    backgroundColor: notification.is_read ? 'transparent' : alpha(theme.palette.primary.main, 0.05),
                    '&:hover': {
                      backgroundColor: notification.is_read
                        ? alpha(theme.palette.action.hover, 0.04)
                        : alpha(theme.palette.primary.main, 0.08)
                    },
                    whiteSpace: 'normal',
                    alignItems: 'flex-start',
                    py: 1.5
                  }}
                >
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: notification.is_read ? 'normal' : 'bold',
                            flex: 1
                          }}
                        >
                          {notification.title}
                        </Typography>
                        {!notification.is_read && (
                          <Box
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              backgroundColor: theme.palette.primary.main
                            }}
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <NotificationRichText
                          text={notification.message}
                          variant="body2"
                          color="text.secondary"
                          sx={{ mb: 0.5 }}
                          showYouTubePreview={true}
                        />
                        <Typography variant="caption" color="text.secondary">
                          {new Date(notification.created_at).toLocaleDateString()}
                        </Typography>
                      </Box>
                    }
                  />
                </MenuItem>
              ))}
              {notifications.length > 5 && (
                <MenuItem disabled>
                  <Typography variant="caption" color="text.secondary">
                    Showing 5 of {notifications.length} notifications
                  </Typography>
                </MenuItem>
              )}

              {/* Divider and action buttons */}
              <Divider sx={{ my: 1 }} />

              {/* View All Notifications Link */}
              <MenuItem
                onClick={() => {
                  setNotificationsAnchorEl(null);
                  onNavigate('notifications');
                }}
                sx={{
                  justifyContent: 'center',
                  py: 1,
                  color: theme.palette.primary.main,
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.08)
                  }
                }}
              >
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  View All Notifications
                </Typography>
              </MenuItem>

              {unreadCount > 0 && (
                <MenuItem onClick={() => { markAllAsRead(); handleNotificationsClose(); }}>
                  <Typography variant="body2" color="primary" align="center" sx={{ width: '100%' }}>
                    Mark all as read
                  </Typography>
                </MenuItem>
              )}
            </>
          )}
        </Menu>
        
        {/* User Menu */}
        <Menu
          anchorEl={userAnchorEl}
          open={Boolean(userAnchorEl)}
          onClose={handleUserMenuClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          <MenuItem onClick={handleProfileClick}>
            <ListItemIcon>
              <PersonIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="My Profile" />
          </MenuItem>
          <MenuItem onClick={handleSettingsClick}>
            <ListItemIcon>
              <SettingsIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="Settings" />
          </MenuItem>
          <MenuItem onClick={handlePasswordModalOpen}> {/* Add Change Password option */}
            <ListItemIcon>
              <PasswordIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="Change Password" />
          </MenuItem>
          <Divider />
          <MenuItem onClick={handleSignOut}>
            <ListItemIcon>
              <ExitToAppIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="Sign out" />
          </MenuItem>
        </Menu>
        
        {/* More Menu (Mobile) */}
        <Menu
          anchorEl={moreMenuAnchorEl}
          open={Boolean(moreMenuAnchorEl)}
          onClose={handleMoreMenuClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          {navItems.map((item) => (
            <MenuItem key={item.name} onClick={() => handleNavClick(item.path)}>
              <ListItemIcon>
                {item.icon}
              </ListItemIcon>
              <ListItemText primary={item.name} />
            </MenuItem>
          ))}
          <Divider />
          {user ? (
            <>
              <MenuItem onClick={handleProfileClick}>
                <ListItemIcon>
                  <PersonIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="My Profile" />
              </MenuItem>
              <MenuItem onClick={handleSettingsClick}>
                <ListItemIcon>
                  <SettingsIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Settings" />
              </MenuItem>
              <MenuItem onClick={handleSignOut}>
                <ListItemIcon>
                  <ExitToAppIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Sign out" />
              </MenuItem>
            </>
          ) : (
            <MenuItem onClick={() => onNavigate('auth/login')}>
              <ListItemIcon>
                <PersonIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Sign In" />
            </MenuItem>
          )}
        </Menu>
        
        {/* Quick Access Menu (Mobile Swipeable Drawer) */}
        <SwipeableDrawer
          anchor="right"
          open={quickMenuOpen}
          onClose={toggleQuickMenu(false)}
          onOpen={toggleQuickMenu(true)}
        >
          <Box
            sx={{ width: 250 }}
            role="presentation"
            onClick={toggleQuickMenu(false)}
            onKeyDown={toggleQuickMenu(false)}
          >
            <List>
              <ListItem>
                <Typography variant="h6">Quick Access</Typography>
              </ListItem>
              <Divider />
              {navItems.map((item) => (
                <ListItem button key={item.name} onClick={() => handleNavClick(item.path)}>
                  <ListItemIcon>
                    {item.icon}
                  </ListItemIcon>
                  <ListItemText primary={item.name} />
                </ListItem>
              ))}
            </List>
          </Box>
        </SwipeableDrawer>
      </Toolbar>

      {/* Share Menu */}
      <Menu
        anchorEl={shareMenuAnchorEl}
        open={Boolean(shareMenuAnchorEl)}
        onClose={handleShareMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        slotProps={{
          paper: {
            sx: {
              minWidth: 250,
              maxWidth: 350
            }
          }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Share this page
          </Typography>
          <SocialShare
            variant="menu"
            url={window.location.href}
            title={title || 'DataStatPro'}
            description="Free Statistical Software for Research & Education"
            platforms={['facebook', 'twitter', 'linkedin', 'email', 'copy']}
            onClose={handleShareMenuClose}
          />
        </Box>
      </Menu>

      {/* Render Password Change Modal */}
      <PasswordChangeModal 
        open={passwordModalOpen} 
        onClose={handlePasswordModalClose} 
      />
    </AppBar>
  );
};

export default AuthAppHeader;
