import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  Chip,
  List,
  ListItem,
  ListItemText,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CardHeader,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  ListItemAvatar,
  Divider
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit,
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  People as PeopleIcon,
  Dataset as DatasetIcon,
  Visibility as VisibilityIcon,
  Download,
  FileDownload as FileDownloadIcon,
  ArrowBack,
  Analytics,
  TrendingUp,
  TrendingDown,
  People,
  Assessment,
  DataUsage,
  Timeline,
  BarChart,
  Pie<PERSON>hart,
  ShowChart,
  Person,
  FileUpload
} from '@mui/icons-material';
import { AuthContext } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import PageTitle from '../../components/UI/PageTitle';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart as RechartsBarChart,
  Bar,
  PieChart as RechartsPieChart,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

interface AnalyticsData {
  period: string;
  users: number;
  datasets: number;
  analyses: number;
  storage: number;
}

interface UserActivity {
  id: string;
  user: string;
  action: string;
  timestamp: string;
  details: string;
}

interface PopularDataset {
  id: string;
  name: string;
  downloads: number;
  views: number;
  category: string;
}

const AnalyticsDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const [isMaximized, setIsMaximized] = useState(false);
  const [timeRange, setTimeRange] = useState('7d');
  const [isLoading, setIsLoading] = useState(true);

  // Mock data - replace with real API calls
  const [analyticsData] = useState<AnalyticsData[]>([
    { period: 'Mon', users: 120, datasets: 45, analyses: 89, storage: 2.3 },
    { period: 'Tue', users: 135, datasets: 52, analyses: 95, storage: 2.8 },
    { period: 'Wed', users: 148, datasets: 38, analyses: 102, storage: 3.1 },
    { period: 'Thu', users: 162, datasets: 61, analyses: 87, storage: 3.5 },
    { period: 'Fri', users: 178, datasets: 49, analyses: 114, storage: 3.9 },
    { period: 'Sat', users: 145, datasets: 33, analyses: 76, storage: 4.2 },
    { period: 'Sun', users: 132, datasets: 41, analyses: 68, storage: 4.1 }
  ]);

  const [userActivities] = useState<UserActivity[]>([
    {
      id: '1',
      user: 'John Smith',
      action: 'Dataset Upload',
      timestamp: '2 minutes ago',
      details: 'sales_data_2024.csv'
    },
    {
      id: '2',
      user: 'Sarah Johnson',
      action: 'Analysis Completed',
      timestamp: '5 minutes ago',
      details: 'Regression Analysis on Customer Data'
    },
    {
      id: '3',
      user: 'Mike Davis',
      action: 'Dataset Download',
      timestamp: '8 minutes ago',
      details: 'market_trends_q4.xlsx'
    },
    {
      id: '4',
      user: 'Emily Chen',
      action: 'New Account Created',
      timestamp: '12 minutes ago',
      details: 'Pro subscription'
    },
    {
      id: '5',
      user: 'David Wilson',
      action: 'Report Generated',
      timestamp: '15 minutes ago',
      details: 'Monthly Performance Report'
    }
  ]);

  const [popularDatasets] = useState<PopularDataset[]>([
    {
      id: '1',
      name: 'Global Economic Indicators',
      downloads: 1247,
      views: 3891,
      category: 'Economics'
    },
    {
      id: '2',
      name: 'Climate Change Data 2024',
      downloads: 892,
      views: 2456,
      category: 'Environment'
    },
    {
      id: '3',
      name: 'Social Media Trends',
      downloads: 756,
      views: 1923,
      category: 'Social'
    },
    {
      id: '4',
      name: 'Healthcare Statistics',
      downloads: 634,
      views: 1567,
      category: 'Healthcare'
    },
    {
      id: '5',
      name: 'Education Performance Metrics',
      downloads: 523,
      views: 1234,
      category: 'Education'
    }
  ]);

  const pieData = [
    { name: 'Active Users', value: 65, color: '#8884d8' },
    { name: 'New Users', value: 25, color: '#82ca9d' },
    { name: 'Returning Users', value: 10, color: '#ffc658' }
  ];

  const categoryData = [
    { name: 'Economics', value: 35 },
    { name: 'Healthcare', value: 28 },
    { name: 'Environment', value: 22 },
    { name: 'Social', value: 15 }
  ];

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleToggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  const handleBackToAdmin = () => {
    navigate('/app/admin-dashboard');
  };

  const handleTimeRangeChange = (event: any) => {
    setTimeRange(event.target.value);
    // Here you would typically fetch new data based on the time range
  };

  const handleRefresh = () => {
    setIsLoading(true);
    // Here you would typically refetch data from the API
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'Dataset Upload':
        return <FileUpload color="primary" />;
      case 'Dataset Download':
        return <Download color="success" />;
      case 'Analysis Completed':
        return <Assessment color="info" />;
      case 'New Account Created':
        return <Person color="secondary" />;
      case 'Report Generated':
        return <BarChart color="warning" />;
      default:
        return <Analytics />;
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'Economics': '#1976d2',
      'Healthcare': '#d32f2f',
      'Environment': '#388e3c',
      'Social': '#f57c00',
      'Education': '#7b1fa2'
    };
    return colors[category] || '#757575';
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: 'background.default',
        transition: 'all 0.3s ease-in-out',
        ...(isMaximized && {
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 9999,
          bgcolor: 'background.paper'
        })
      }}
    >
      <Container
        maxWidth={isMaximized ? false : 'xl'}
        sx={{
          py: 3,
          px: isMaximized ? 3 : undefined,
          height: isMaximized ? '100vh' : 'auto',
          overflow: isMaximized ? 'auto' : 'visible'
        }}
      >
        <PageTitle
          title="Analytics Dashboard"
          description="Comprehensive analytics and usage insights"
          icon={<AnalyticsIcon />}
          breadcrumbs={[
            {
              label: 'Admin Dashboard',
              onClick: () => navigate('/app/admin-dashboard')
            },
            {
              label: 'Analytics Dashboard'
            }
          ]}
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Time Range</InputLabel>
                <Select
                  value={timeRange}
                  label="Time Range"
                  onChange={(e) => setTimeRange(e.target.value)}
                >
                  <MenuItem value="7d">Last 7 days</MenuItem>
                  <MenuItem value="30d">Last 30 days</MenuItem>
                  <MenuItem value="90d">Last 90 days</MenuItem>
                  <MenuItem value="1y">Last year</MenuItem>
                </Select>
              </FormControl>
              <Tooltip title="Refresh Data">
                <IconButton
                  onClick={handleRefresh}
                  sx={{
                    bgcolor: 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title={isMaximized ? 'Minimize' : 'Maximize'}>
                <IconButton
                  onClick={handleToggleMaximize}
                  sx={{
                    bgcolor: 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                >
                  {isMaximized ? <FullscreenExit /> : <FullscreenIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          }
        />

        {/* Key Metrics */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="body2">
                      Total Users
                    </Typography>
                    <Typography variant="h4" component="div">
                      1,247
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <TrendingUp color="success" fontSize="small" />
                      <Typography variant="body2" color="success.main" sx={{ ml: 0.5 }}>
                        +12.5%
                      </Typography>
                    </Box>
                  </Box>
                  <Avatar sx={{ bgcolor: 'primary.main', width: 56, height: 56 }}>
                    <PeopleIcon />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="body2">
                      Datasets
                    </Typography>
                    <Typography variant="h4" component="div">
                      3,891
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <TrendingUp color="success" fontSize="small" />
                      <Typography variant="body2" color="success.main" sx={{ ml: 0.5 }}>
                        +8.2%
                      </Typography>
                    </Box>
                  </Box>
                  <Avatar sx={{ bgcolor: 'info.main', width: 56, height: 56 }}>
                    <DataUsage />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="body2">
                      Analyses
                    </Typography>
                    <Typography variant="h4" component="div">
                      15,632
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <TrendingDown color="error" fontSize="small" />
                      <Typography variant="body2" color="error.main" sx={{ ml: 0.5 }}>
                        -2.1%
                      </Typography>
                    </Box>
                  </Box>
                  <Avatar sx={{ bgcolor: 'success.main', width: 56, height: 56 }}>
                    <Assessment />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="textSecondary" gutterBottom variant="body2">
                      Storage Used
                    </Typography>
                    <Typography variant="h4" component="div">
                      2.4 TB
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <TrendingUp color="warning" fontSize="small" />
                      <Typography variant="body2" color="warning.main" sx={{ ml: 0.5 }}>
                        +15.3%
                      </Typography>
                    </Box>
                  </Box>
                  <Avatar sx={{ bgcolor: 'warning.main', width: 56, height: 56 }}>
                    <Timeline />
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Grid container spacing={3}>
          {/* User Activity Chart */}
          <Grid item xs={12} lg={8}>
            <Card>
              <CardHeader
                title="User Activity Trends"
                avatar={<ShowChart />}
                subheader="Daily active users and platform usage"
              />
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={analyticsData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <RechartsTooltip />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="users"
                      stackId="1"
                      stroke="#8884d8"
                      fill="#8884d8"
                      name="Users"
                    />
                    <Area
                      type="monotone"
                      dataKey="analyses"
                      stackId="1"
                      stroke="#82ca9d"
                      fill="#82ca9d"
                      name="Analyses"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* User Distribution */}
          <Grid item xs={12} lg={4}>
            <Card>
              <CardHeader
                title="User Distribution"
                avatar={<PieChart />}
                subheader="User engagement breakdown"
              />
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <RechartsTooltip />
                    <RechartsPieChart data={pieData} cx="50%" cy="50%" outerRadius={80}>
                      {pieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </RechartsPieChart>
                    <Legend />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Dataset Categories */}
          <Grid item xs={12} lg={6}>
            <Card>
              <CardHeader
                title="Dataset Categories"
                avatar={<BarChart />}
                subheader="Popular dataset categories"
              />
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsBarChart data={categoryData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <RechartsTooltip />
                    <Bar dataKey="value" fill="#8884d8" />
                  </RechartsBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Recent Activity */}
          <Grid item xs={12} lg={6}>
            <Card>
              <CardHeader
                title="Recent Activity"
                avatar={<Timeline />}
                subheader="Latest user actions"
              />
              <CardContent>
                <List>
                  {userActivities.map((activity, index) => (
                    <React.Fragment key={activity.id}>
                      <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            {getActionIcon(activity.action)}
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="subtitle2">
                                {activity.user}
                              </Typography>
                              <Chip size="small" label={activity.action} variant="outlined" />
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                {activity.details}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {activity.timestamp}
                              </Typography>
                            </Box>
                          }
                        />
                      </ListItem>
                      {index < userActivities.length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Popular Datasets */}
          <Grid item xs={12}>
            <Card>
              <CardHeader
                title="Popular Datasets"
                avatar={<DataUsage />}
                subheader="Most accessed datasets this period"
              />
              <CardContent>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Dataset Name</TableCell>
                        <TableCell>Category</TableCell>
                        <TableCell align="right">Views</TableCell>
                        <TableCell align="right">Downloads</TableCell>
                        <TableCell align="right">Engagement</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {popularDatasets.map((dataset) => (
                        <TableRow key={dataset.id} hover>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Avatar sx={{ width: 32, height: 32, bgcolor: getCategoryColor(dataset.category) }}>
                                <VisibilityIcon fontSize="small" />
                              </Avatar>
                              <Typography variant="subtitle2">
                                {dataset.name}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              size="small"
                              label={dataset.category}
                              sx={{ bgcolor: getCategoryColor(dataset.category), color: 'white' }}
                            />
                          </TableCell>
                          <TableCell align="right">
                            <Typography variant="body2">
                              {dataset.views.toLocaleString()}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Typography variant="body2">
                              {dataset.downloads.toLocaleString()}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Typography variant="body2" color="success.main">
                              {((dataset.downloads / dataset.views) * 100).toFixed(1)}%
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default AnalyticsDashboardPage;