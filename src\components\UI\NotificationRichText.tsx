import React from 'react';
import { Typography, Box } from '@mui/material';
import { parseRichText, ParsedSegment } from '../../utils/richTextUtils';
import YouTubeLink from './YouTubeLink';
import GeneralLink from './GeneralLink';

interface NotificationRichTextProps {
  text: string;
  variant?: 'body2' | 'body1' | 'caption';
  color?: string;
  sx?: any;
  showYouTubePreview?: boolean;
}

const NotificationRichText: React.FC<NotificationRichTextProps> = ({
  text,
  variant = 'body2',
  color = 'text.secondary',
  sx = {},
  showYouTubePreview = true
}) => {
  const segments = parseRichText(text);

  // If only plain text, render as regular text
  if (segments.length === 1 && segments[0].type === 'text') {
    return (
      <Typography 
        variant={variant} 
        color={color} 
        sx={{
          whiteSpace: 'pre-wrap',
          wordWrap: 'break-word',
          lineHeight: 1.4,
          ...sx
        }}
      >
        {text}
      </Typography>
    );
  }

  // Render rich text with all formatting
  return (
    <Typography 
      variant={variant} 
      color={color} 
      component="div"
      sx={{
        whiteSpace: 'pre-wrap',
        wordWrap: 'break-word',
        lineHeight: 1.4,
        ...sx
      }}
    >
      {segments.map((segment, index) => {
        switch (segment.type) {
          case 'text':
            return (
              <span key={index}>
                {segment.content}
              </span>
            );
            
          case 'youtube':
            if (segment.info) {
              return (
                <Box 
                  key={index} 
                  component="span" 
                  sx={{ 
                    display: 'inline-block',
                    verticalAlign: 'middle',
                    mx: 0.5
                  }}
                >
                  <YouTubeLink
                    url={segment.content}
                    info={segment.info}
                    variant="inline"
                    showPreview={showYouTubePreview}
                  />
                </Box>
              );
            }
            return null;
            
          case 'link':
            return (
              <Box 
                key={index} 
                component="span" 
                sx={{ 
                  display: 'inline-block',
                  verticalAlign: 'middle',
                  mx: 0.5
                }}
              >
                <GeneralLink
                  url={segment.url || segment.content}
                  variant="inline"
                  showPreview={showYouTubePreview}
                />
              </Box>
            );
            
          case 'bold':
            return (
              <Box 
                key={index} 
                component="span" 
                sx={{ fontWeight: 'bold' }}
              >
                {segment.content}
              </Box>
            );
            
          case 'italic':
            return (
              <Box 
                key={index} 
                component="span" 
                sx={{ fontStyle: 'italic' }}
              >
                {segment.content}
              </Box>
            );
            
          case 'linebreak':
            return <br key={index} />;
            
          default:
            return (
              <span key={index}>
                {segment.content}
              </span>
            );
        }
      })}
    </Typography>
  );
};

export default NotificationRichText;
