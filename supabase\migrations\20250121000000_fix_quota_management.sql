-- Fix quota management issues
-- 1. Update get_all_user_quotas to return total count for pagination
-- 2. Fix any remaining issues with storage calculation

-- Drop existing functions first to avoid conflicts
DROP FUNCTION IF EXISTS get_all_user_quotas(integer, integer, text);
DROP FUNCTION IF EXISTS get_user_quota_usage(uuid);

CREATE OR REPLACE FUNCTION get_all_user_quotas(
    p_limit integer DEFAULT 50,
    p_offset integer DEFAULT 0,
    p_search text DEFAULT NULL
)
RETURNS TABLE(
    user_id uuid,
    email text,
    full_name text,
    tier_name text,
    dataset_limit integer,
    storage_limit_mb integer,
    is_custom_quota boolean,
    dataset_count bigint,
    storage_used_mb numeric,
    dataset_usage_percent numeric,
    storage_usage_percent numeric,
    total_count bigint
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    total_users bigint;
BEGIN
    -- Check if user is admin
    IF NOT is_user_admin(auth.uid()) THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;

    -- Get total count for pagination
    SELECT COUNT(*) INTO total_users
    FROM auth.users au
    JOIN public.profiles p ON au.id = p.id
    WHERE (p_search IS NULL OR 
           p.full_name ILIKE '%' || p_search || '%' OR 
           au.email ILIKE '%' || p_search || '%');

    -- Return paginated results with total count
    RETURN QUERY
    SELECT 
        au.id as user_id,
        au.email::text,
        COALESCE(p.full_name, 'No name')::text as full_name,
        eq.tier_name::text,
        eq.dataset_limit,
        eq.storage_limit_mb,
        (uq.user_id IS NOT NULL) as is_custom_quota,
        COALESCE(usage.dataset_count, 0) as dataset_count,
        COALESCE(usage.storage_used_mb, 0) as storage_used_mb,
        CASE 
            WHEN eq.dataset_limit > 0 THEN 
                ROUND((COALESCE(usage.dataset_count, 0)::numeric / eq.dataset_limit::numeric) * 100, 2)
            ELSE 0
        END as dataset_usage_percent,
        CASE 
            WHEN eq.storage_limit_mb > 0 THEN 
                ROUND((COALESCE(usage.storage_used_mb, 0) / eq.storage_limit_mb::numeric) * 100, 2)
            ELSE 0
        END as storage_usage_percent,
        total_users as total_count
    FROM auth.users au
    JOIN public.profiles p ON au.id = p.id
    LEFT JOIN public.user_quotas uq ON au.id = uq.user_id
    LEFT JOIN LATERAL get_user_effective_quota(au.id) eq ON true
    LEFT JOIN LATERAL get_user_quota_usage(au.id) usage ON true
    WHERE (p_search IS NULL OR 
           p.full_name ILIKE '%' || p_search || '%' OR 
           au.email ILIKE '%' || p_search || '%')
    ORDER BY p.full_name, au.email
    LIMIT p_limit
    OFFSET p_offset;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_all_user_quotas(integer, integer, text) TO authenticated;

-- Ensure get_user_quota_usage function is using correct storage calculation
-- (This should already be correct based on previous analysis, but let's verify)
CREATE OR REPLACE FUNCTION get_user_quota_usage(p_user_id uuid)
RETURNS TABLE(
    dataset_count bigint,
    storage_used_mb numeric
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(DISTINCT d.id) as dataset_count,
        COALESCE(
            ROUND(
                SUM(
                    CASE 
                        WHEN so.metadata->>'size' IS NOT NULL 
                        THEN (so.metadata->>'size')::bigint 
                        ELSE 0 
                    END
                )::numeric / (1024 * 1024), 2
            ), 0
        ) as storage_used_mb
    FROM public.user_datasets d
    LEFT JOIN storage.objects so ON (
        so.name LIKE d.id::text || '/%' AND 
        so.bucket_id = 'user-datasets'
    )
    WHERE d.user_id = p_user_id;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_user_quota_usage(uuid) TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION get_all_user_quotas(integer, integer, text) IS 'Admin function to get all users with their quota information, usage statistics, and total count for pagination';
COMMENT ON FUNCTION get_user_quota_usage(uuid) IS 'Function to calculate user dataset count and storage usage in MB from actual file sizes';