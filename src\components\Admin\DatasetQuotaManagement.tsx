import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  LinearProgress,
  Alert,
  CircularProgress,
  InputAdornment,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  Pagination,
  Switch,
  FormControlLabel,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  Edit as EditIcon,
  Storage as StorageIcon,
  Dataset as DatasetIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  Star as StarIcon,
  Refresh as RefreshIcon,
  History as HistoryIcon,
  Settings as SettingsIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { supabase } from '../../utils/supabaseClient';

interface UserQuota {
  user_id: string;
  email: string;
  full_name: string;
  tier_name: string;
  dataset_limit: number;
  storage_limit_mb: number;
  is_custom_quota: boolean;
  dataset_count: number;
  storage_used_mb: number;
  dataset_usage_percent: number;
  storage_usage_percent: number;
}

interface TierQuota {
  tier_name: string;
  dataset_limit: number;
  storage_limit_mb: number;
}

interface AuditLogEntry {
  id: string;
  target_user_email: string;
  target_user_name: string;
  admin_user_email: string;
  admin_user_name: string;
  action_type: string;
  old_values: any;
  new_values: any;
  reason: string;
  created_at: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`quota-tabpanel-${index}`}
      aria-labelledby={`quota-tab-${index}`}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
};

interface EditQuotaDialogProps {
  open: boolean;
  user: UserQuota | null;
  onClose: () => void;
  onSave: (userId: string, datasetLimit: number, storageLimit: number, reason: string) => Promise<void>;
}

const EditQuotaDialog: React.FC<EditQuotaDialogProps> = ({ open, user, onClose, onSave }) => {
  const [datasetLimit, setDatasetLimit] = useState<number>(0);
  const [storageLimit, setStorageLimit] = useState<number>(0);
  const [reason, setReason] = useState<string>('');
  const [saving, setSaving] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  useEffect(() => {
    if (user) {
      setDatasetLimit(user.dataset_limit);
      setStorageLimit(user.storage_limit_mb);
      setReason('');
    }
  }, [user]);

  const handleSave = async () => {
    if (!user) return;

    // Check if limits are being reduced and user would exceed new limits
    const wouldExceedDatasets = datasetLimit < user.dataset_count;
    const wouldExceedStorage = storageLimit < user.storage_used_mb;

    if ((wouldExceedDatasets || wouldExceedStorage) && !showConfirmation) {
      setShowConfirmation(true);
      return;
    }

    setSaving(true);
    try {
      await onSave(user.user_id, datasetLimit, storageLimit, reason);
      onClose();
      setShowConfirmation(false);
    } catch (error) {
      console.error('Error saving quota:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setShowConfirmation(false);
    onClose();
  };

  if (!user) return null;

  return (
    <Dialog open={open} onClose={handleCancel} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <EditIcon />
          Edit Quota: {user.full_name || user.email}
        </Box>
      </DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          {/* Current Usage Display */}
          <Card sx={{ mb: 3, bgcolor: 'background.default' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Current Usage
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <DatasetIcon color="primary" />
                    <Typography variant="body2">
                      Datasets: {user.dataset_count} / {user.dataset_limit}
                    </Typography>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={user.dataset_usage_percent} 
                    color={user.dataset_usage_percent > 80 ? 'warning' : 'primary'}
                  />
                </Grid>
                <Grid item xs={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <StorageIcon color="secondary" />
                    <Typography variant="body2">
                      Storage: {user.storage_used_mb.toFixed(2)} MB / {user.storage_limit_mb} MB
                    </Typography>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={user.storage_usage_percent} 
                    color={user.storage_usage_percent > 80 ? 'warning' : 'secondary'}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Quota Settings */}
          <Grid container spacing={3}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Dataset Limit"
                type="number"
                value={datasetLimit}
                onChange={(e) => setDatasetLimit(Math.max(0, parseInt(e.target.value) || 0))}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <DatasetIcon />
                    </InputAdornment>
                  ),
                }}
                helperText={`Current: ${user.dataset_count} datasets`}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Storage Limit (MB)"
                type="number"
                value={storageLimit}
                onChange={(e) => setStorageLimit(Math.max(0, parseInt(e.target.value) || 0))}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <StorageIcon />
                    </InputAdornment>
                  ),
                }}
                helperText={`Current: ${user.storage_used_mb.toFixed(2)} MB used`}
              />
            </Grid>
          </Grid>

          <TextField
            fullWidth
            label="Reason for Change"
            multiline
            rows={3}
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            sx={{ mt: 3 }}
            placeholder="Optional: Explain why you're changing this user's quota..."
          />

          {/* Warning Messages */}
          {datasetLimit < user.dataset_count && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>Warning:</strong> The new dataset limit ({datasetLimit}) is lower than the user's current dataset count ({user.dataset_count}). 
                The user may need to delete some datasets to comply with the new limit.
              </Typography>
            </Alert>
          )}

          {storageLimit < user.storage_used_mb && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>Warning:</strong> The new storage limit ({storageLimit} MB) is lower than the user's current usage ({user.storage_used_mb.toFixed(2)} MB). 
                The user may need to delete some data to comply with the new limit.
              </Typography>
            </Alert>
          )}

          {/* Confirmation Dialog */}
          {showConfirmation && (
            <Alert severity="error" sx={{ mt: 2 }}>
              <Typography variant="body2" gutterBottom>
                <strong>Confirmation Required:</strong> You are setting limits below the user's current usage. 
                This may prevent the user from accessing their data until they reduce their usage.
              </Typography>
              <Typography variant="body2">
                Are you sure you want to proceed?
              </Typography>
            </Alert>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCancel} disabled={saving}>
          Cancel
        </Button>
        {showConfirmation ? (
          <>
            <Button 
              onClick={() => setShowConfirmation(false)} 
              disabled={saving}
              color="primary"
            >
              Review
            </Button>
            <Button 
              onClick={handleSave} 
              variant="contained" 
              color="error"
              disabled={saving}
              startIcon={saving ? <CircularProgress size={16} /> : <WarningIcon />}
            >
              {saving ? 'Applying...' : 'Confirm & Apply'}
            </Button>
          </>
        ) : (
          <Button 
            onClick={handleSave} 
            variant="contained" 
            disabled={saving}
            startIcon={saving ? <CircularProgress size={16} /> : undefined}
          >
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

interface TierQuotaDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (tierName: string, datasetLimit: number, storageLimit: number, reason: string) => Promise<void>;
  tierQuotas: TierQuota[];
  preselectedTier?: string;
}

const TierQuotaDialog: React.FC<TierQuotaDialogProps> = ({ open, onClose, onSave, tierQuotas, preselectedTier }) => {
  const [selectedTier, setSelectedTier] = useState<string>('');
  const [datasetLimit, setDatasetLimit] = useState<number>(0);
  const [storageLimit, setStorageLimit] = useState<number>(0);
  const [reason, setReason] = useState<string>('');
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (open && preselectedTier && !selectedTier) {
      setSelectedTier(preselectedTier);
    }
  }, [open, preselectedTier, selectedTier]);

  useEffect(() => {
    if (selectedTier && tierQuotas.length > 0) {
      const tier = tierQuotas.find(t => t.tier_name === selectedTier);
      if (tier) {
        setDatasetLimit(tier.dataset_limit);
        setStorageLimit(tier.storage_limit_mb);
      }
    }
  }, [selectedTier, tierQuotas]);

  const handleSave = async () => {
    if (!selectedTier) return;

    setSaving(true);
    try {
      await onSave(selectedTier, datasetLimit, storageLimit, reason);
      handleClose();
    } catch (error) {
      console.error('Error saving tier quota:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    setSelectedTier('');
    setReason('');
    onClose();
  };

  const getTierIcon = (tierName: string) => {
    switch (tierName) {
      case 'pro': return <StarIcon />;
      case 'edu':
      case 'edu_pro': return <SchoolIcon />;
      default: return <PersonIcon />;
    }
  };

  const getTierLabel = (tierName: string) => {
    const labels: { [key: string]: string } = {
      'guest': 'Guest',
      'standard': 'Standard',
      'pro': 'Pro',
      'edu': 'Educational',
      'edu_pro': 'Educational Pro'
    };
    return labels[tierName] || tierName;
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SettingsIcon />
          Edit Tier Default Quotas
        </Box>
      </DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <FormControl fullWidth sx={{ mb: 3 }}>
            <InputLabel>Account Tier</InputLabel>
            <Select
              value={selectedTier}
              label="Account Tier"
              onChange={(e) => setSelectedTier(e.target.value)}
            >
              {tierQuotas.map((tier) => (
                <MenuItem key={tier.tier_name} value={tier.tier_name}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getTierIcon(tier.tier_name)}
                    {getTierLabel(tier.tier_name)}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {selectedTier && (
            <>
              <Grid container spacing={3}>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Dataset Limit"
                    type="number"
                    value={datasetLimit}
                    onChange={(e) => setDatasetLimit(Math.max(0, parseInt(e.target.value) || 0))}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <DatasetIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Storage Limit (MB)"
                    type="number"
                    value={storageLimit}
                    onChange={(e) => setStorageLimit(Math.max(0, parseInt(e.target.value) || 0))}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <StorageIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
              </Grid>

              <TextField
                fullWidth
                label="Reason for Change"
                multiline
                rows={3}
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                sx={{ mt: 3 }}
                placeholder="Optional: Explain why you're changing this tier's default quota..."
              />

              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Note:</strong> Changing tier defaults will affect all users of this tier who don't have custom quota overrides.
                </Typography>
              </Alert>
            </>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={saving}>
          Cancel
        </Button>
        <Button 
          onClick={handleSave} 
          variant="contained" 
          disabled={saving || !selectedTier}
          startIcon={saving ? <CircularProgress size={16} /> : undefined}
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const DatasetQuotaManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [users, setUsers] = useState<UserQuota[]>([]);
  const [tierQuotas, setTierQuotas] = useState<TierQuota[]>([]);
  const [auditLog, setAuditLog] = useState<AuditLogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [tierDialogOpen, setTierDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserQuota | null>(null);
  const [preselectedTier, setPreselectedTier] = useState<string | undefined>(undefined);
  const pageSize = 25;

  useEffect(() => {
    fetchData();
  }, [page, searchTerm]);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      await Promise.all([
        fetchUsers(),
        fetchTierQuotas(),
        activeTab === 2 ? fetchAuditLog() : Promise.resolve()
      ]);
    } catch (err: any) {
      console.error('Error fetching data:', err);
      setError(err.message || 'Failed to load quota data');
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    const { data, error } = await supabase.rpc('get_all_user_quotas', {
      p_limit: pageSize,
      p_offset: (page - 1) * pageSize,
      p_search: searchTerm || null
    });

    if (error) throw error;
    setUsers(data || []);
    
    // Get total count from the first row (all rows have the same total_count)
    const totalCount = data && data.length > 0 ? data[0].total_count : 0;
    setTotalPages(Math.max(1, Math.ceil(totalCount / pageSize)));
  };

  const fetchTierQuotas = async () => {
    const { data, error } = await supabase
      .from('tier_quotas')
      .select('tier_name, dataset_limit, storage_limit_mb')
      .order('tier_name');

    if (error) throw error;
    setTierQuotas(data || []);
  };

  const fetchAuditLog = async () => {
    const { data, error } = await supabase.rpc('get_quota_audit_trail', {
      target_user_id_param: null,
      page_size: 50,
      page_offset: 0
    });

    if (error) throw error;
    setAuditLog(data || []);
  };

  const handleEditUser = (user: UserQuota) => {
    setSelectedUser(user);
    setEditDialogOpen(true);
  };

  const handleSaveUserQuota = async (userId: string, datasetLimit: number, storageLimit: number, reason: string) => {
    const { error } = await supabase.rpc('set_user_quota', {
      target_user_id: userId,
      new_dataset_limit: datasetLimit,
      new_storage_limit_mb: storageLimit,
      reason_text: reason || null
    });

    if (error) throw error;
    await fetchUsers();
  };

  const handleSaveTierQuota = async (tierName: string, datasetLimit: number, storageLimit: number, reason: string) => {
    const { error } = await supabase.rpc('set_tier_quota', {
      tier_name_param: tierName,
      new_dataset_limit: datasetLimit,
      new_storage_limit_mb: storageLimit,
      reason_text: reason || null
    });

    if (error) throw error;
    await Promise.all([fetchUsers(), fetchTierQuotas()]);
  };

  const handleRemoveUserQuota = async (userId: string) => {
    const { error } = await supabase.rpc('remove_user_quota', {
      target_user_id: userId,
      reason_text: 'Reverted to tier default via admin dashboard'
    });

    if (error) throw error;
    await fetchUsers();
  };

  const getAccountTypeChip = (tierName: string, isCustom: boolean) => {
    const config = {
      guest: { label: 'Guest', color: 'default' as const, icon: <PersonIcon /> },
      standard: { label: 'Standard', color: 'primary' as const, icon: <PersonIcon /> },
      pro: { label: 'Pro', color: 'warning' as const, icon: <StarIcon /> },
      edu: { label: 'Educational', color: 'info' as const, icon: <SchoolIcon /> },
      edu_pro: { label: 'Edu Pro', color: 'secondary' as const, icon: <SchoolIcon /> }
    };

    const { label, color, icon } = config[tierName as keyof typeof config] || config.standard;

    return (
      <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
        <Chip
          label={label}
          color={color}
          size="small"
          icon={icon}
          variant="outlined"
        />
        {isCustom && (
          <Chip
            label="Custom"
            color="error"
            size="small"
            variant="filled"
          />
        )}
      </Box>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading && users.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={48} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading quota management...
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', height: '100%' }}>
      {/* Header */}
      <Box sx={{
        mb: 3,
        display: 'flex',
        flexDirection: { xs: 'column', sm: 'row' },
        justifyContent: 'space-between',
        alignItems: { xs: 'flex-start', sm: 'center' },
        gap: 2
      }}>
        <Box>
          <Typography variant="h4" gutterBottom sx={{ fontSize: { xs: '1.5rem', sm: '2rem' } }}>
            Dataset & Storage Quotas
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
            Manage user dataset limits and storage quotas
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<SettingsIcon />}
            onClick={() => {
              setPreselectedTier(undefined);
              setTierDialogOpen(true);
            }}
          >
            Tier Defaults
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchData}
            disabled={loading}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={(_, newValue) => {
            setActiveTab(newValue);
            if (newValue === 2) fetchAuditLog();
          }}
          variant="fullWidth"
        >
          <Tab label="User Quotas" icon={<PersonIcon />} />
          <Tab label="Tier Defaults" icon={<SettingsIcon />} />
          <Tab label="Audit Log" icon={<HistoryIcon />} />
        </Tabs>
      </Paper>

      {/* Tab Panels */}
      <TabPanel value={activeTab} index={0}>
        {/* Search */}
        <TextField
          fullWidth
          placeholder="Search users by name or email..."
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setPage(1);
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ mb: 3 }}
        />

        {/* Users Table */}
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>User</TableCell>
                <TableCell>Account Type</TableCell>
                <TableCell align="center">Dataset Usage</TableCell>
                <TableCell align="center">Storage Usage</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.user_id}>
                  <TableCell>
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {user.full_name || 'No name'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {user.email}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    {getAccountTypeChip(user.tier_name, user.is_custom_quota)}
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ minWidth: 120 }}>
                      <Typography variant="body2" gutterBottom>
                        {user.dataset_count} / {user.dataset_limit}
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={user.dataset_usage_percent}
                        color={user.dataset_usage_percent > 80 ? 'warning' : 'primary'}
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                      <Typography variant="caption" color="text.secondary">
                        {user.dataset_usage_percent.toFixed(1)}%
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ minWidth: 120 }}>
                      <Typography variant="body2" gutterBottom>
                        {user.storage_used_mb.toFixed(1)} / {user.storage_limit_mb} MB
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={user.storage_usage_percent}
                        color={user.storage_usage_percent > 80 ? 'warning' : 'secondary'}
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                      <Typography variant="caption" color="text.secondary">
                        {user.storage_usage_percent.toFixed(1)}%
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                      <Tooltip title="Edit Quota">
                        <IconButton
                          size="small"
                          onClick={() => handleEditUser(user)}
                          color="primary"
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      {user.is_custom_quota && (
                        <Tooltip title="Remove Custom Quota">
                          <IconButton
                            size="small"
                            onClick={() => handleRemoveUserQuota(user.user_id)}
                            color="error"
                          >
                            <CancelIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        {totalPages > 1 && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
            <Pagination
              count={totalPages}
              page={page}
              onChange={(_, newPage) => setPage(newPage)}
              color="primary"
            />
          </Box>
        )}
      </TabPanel>

      <TabPanel value={activeTab} index={1}>
        {/* Tier Quotas Display */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Default Tier Quotas</Typography>
          <Button
             variant="contained"
             startIcon={<EditIcon />}
             onClick={() => {
               setPreselectedTier(undefined);
               setTierDialogOpen(true);
             }}
             color="primary"
           >
             Edit Tier Quotas
           </Button>
        </Box>
        <Grid container spacing={3}>
          {tierQuotas.map((tier) => (
            <Grid item xs={12} sm={6} md={4} key={tier.tier_name}>
              <Card sx={{ position: 'relative' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {tier.tier_name === 'pro' && <StarIcon color="warning" />}
                      {(tier.tier_name === 'edu' || tier.tier_name === 'edu_pro') && <SchoolIcon color="info" />}
                      {(tier.tier_name === 'standard' || tier.tier_name === 'guest') && <PersonIcon />}
                      <Typography variant="h6">
                        {tier.tier_name.charAt(0).toUpperCase() + tier.tier_name.slice(1).replace('_', ' ')}
                      </Typography>
                    </Box>
                    <Tooltip title="Edit this tier's quotas">
                       <IconButton
                         size="small"
                         onClick={() => {
                           setPreselectedTier(tier.tier_name);
                           setTierDialogOpen(true);
                         }}
                         color="primary"
                       >
                         <EditIcon fontSize="small" />
                       </IconButton>
                     </Tooltip>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <DatasetIcon color="primary" fontSize="small" />
                    <Typography variant="body2">
                      {tier.dataset_limit} datasets
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <StorageIcon color="secondary" fontSize="small" />
                    <Typography variant="body2">
                      {tier.storage_limit_mb} MB storage
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      <TabPanel value={activeTab} index={2}>
        {/* Audit Log */}
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Date</TableCell>
                <TableCell>Target User</TableCell>
                <TableCell>Admin</TableCell>
                <TableCell>Action</TableCell>
                <TableCell>Changes</TableCell>
                <TableCell>Reason</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {auditLog.map((entry) => (
                <TableRow key={entry.id}>
                  <TableCell>
                    <Typography variant="body2">
                      {formatDate(entry.created_at)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {entry.target_user_name || 'No name'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {entry.target_user_email}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {entry.admin_user_name || 'No name'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {entry.admin_user_email}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={entry.action_type.replace('_', ' ')}
                      size="small"
                      color={entry.action_type.includes('remove') ? 'error' : 'primary'}
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem' }}>
                      {JSON.stringify({
                        old: entry.old_values,
                        new: entry.new_values
                      }, null, 2)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {entry.reason || 'No reason provided'}
                    </Typography>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Dialogs */}
      <EditQuotaDialog
        open={editDialogOpen}
        user={selectedUser}
        onClose={() => {
          setEditDialogOpen(false);
          setSelectedUser(null);
        }}
        onSave={handleSaveUserQuota}
      />

      <TierQuotaDialog
        open={tierDialogOpen}
        onClose={() => {
          setTierDialogOpen(false);
          setPreselectedTier(undefined);
        }}
        onSave={handleSaveTierQuota}
        tierQuotas={tierQuotas}
        preselectedTier={preselectedTier}
      />
    </Box>
  );
};

export default DatasetQuotaManagement;