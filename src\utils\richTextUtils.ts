// Enhanced text parsing utilities for rich text formatting and hyperlink handling
import { YouTubeVideoInfo, getYouTubeVideoInfo, isYouTubeUrl } from './youtubeUtils';

export interface ParsedSegment {
  type: 'text' | 'youtube' | 'link' | 'bold' | 'italic' | 'linebreak';
  content: string;
  info?: YouTubeVideoInfo;
  url?: string;
}

/**
 * Detects if a string is a valid URL
 */
export function isValidUrl(text: string): boolean {
  try {
    const url = new URL(text);
    return url.protocol === 'http:' || url.protocol === 'https:';
  } catch {
    return false;
  }
}

/**
 * Extracts all URLs from text (both YouTube and general URLs)
 */
export function findAllUrls(text: string): Array<{ url: string; isYouTube: boolean; info?: YouTubeVideoInfo }> {
  // Enhanced URL regex that captures more URL formats
  const urlRegex = /https?:\/\/(?:[-\w.])+(?:[:\d]+)?(?:\/(?:[\w\/_.])*)?(?:\?(?:[&\w\/.=])*)?(?:#(?:[\w\/.])*)?/g;
  const matches = text.match(urlRegex) || [];
  
  return matches.map(url => {
    const isYouTube = isYouTubeUrl(url);
    const info = isYouTube ? getYouTubeVideoInfo(url) || undefined : undefined;
    return { url, isYouTube, info };
  });
}

/**
 * Processes line breaks and converts them to proper segments
 */
export function processLineBreaks(text: string): string[] {
  // Split by various line break patterns
  return text.split(/\r?\n|\r/).map(line => line.trim());
}

/**
 * Processes markdown-style formatting (bold and italic)
 */
export function processMarkdownFormatting(text: string): ParsedSegment[] {
  const segments: ParsedSegment[] = [];
  let currentIndex = 0;
  
  // Regex patterns for markdown formatting
  const boldRegex = /\*\*(.*?)\*\*/g;
  const italicRegex = /\*(.*?)\*/g;
  
  // Find all formatting matches
  const allMatches: Array<{ type: 'bold' | 'italic'; match: RegExpMatchArray; content: string }> = [];
  
  let match: RegExpExecArray | null;
  
  // Find bold matches
  boldRegex.lastIndex = 0;
  while ((match = boldRegex.exec(text)) !== null) {
    allMatches.push({ type: 'bold', match, content: match[1] });
  }
  
  // Find italic matches (but avoid those already captured by bold)
  italicRegex.lastIndex = 0;
  while ((match = italicRegex.exec(text)) !== null) {
    // Check if this italic match is not inside a bold match
    const isInsideBold = allMatches.some(boldMatch => 
      match && match.index !== undefined &&
      boldMatch.match.index !== undefined &&
      match.index >= boldMatch.match.index && 
      match.index + match[0].length <= boldMatch.match.index + boldMatch.match[0].length
    );
    
    if (!isInsideBold) {
      allMatches.push({ type: 'italic', match, content: match[1] });
    }
  }
  
  // Filter out overlapping matches (prioritize bold over italic)
  const filteredMatches = allMatches.filter((match) => {
    if (match.type === 'italic' && match.match.index !== undefined) {
      // Check if this italic match overlaps with any bold match
      const boldMatch = allMatches.find(m => 
        m.type === 'bold' && 
        m.match.index !== undefined &&
        match.match.index !== undefined &&
        m.match.index <= match.match.index && 
        match.match.index + match.match[0].length <= m.match.index + m.match[0].length
      );
      return !boldMatch;
    }
    return true;
  });

  // Sort matches by position
  const validMatches = filteredMatches.filter(m => m.match.index !== undefined);
  validMatches.sort((a, b) => (a.match.index || 0) - (b.match.index || 0));
  
  // Process matches and create segments
  validMatches.forEach(({ type, match, content }) => {
    const matchIndex = match.index || 0;
    
    // Add text before the match
    if (matchIndex > currentIndex) {
      const beforeText = text.substring(currentIndex, matchIndex);
      if (beforeText) {
        segments.push({ type: 'text', content: beforeText });
      }
    }
    
    // Add the formatted segment
    segments.push({ type, content });
    
    currentIndex = matchIndex + match[0].length;
  });
  
  // Add remaining text
  if (currentIndex < text.length) {
    const remainingText = text.substring(currentIndex);
    if (remainingText) {
      segments.push({ type: 'text', content: remainingText });
    }
  }
  
  // If no formatting found, return the original text as a single segment
  if (segments.length === 0) {
    segments.push({ type: 'text', content: text });
  }
  
  return segments;
}

/**
 * Enhanced text parser that handles URLs, line breaks, and markdown formatting
 */
export function parseRichText(text: string): ParsedSegment[] {
  if (!text || text.trim() === '') {
    return [{ type: 'text', content: text }];
  }
  
  const segments: ParsedSegment[] = [];
  
  // First, split by line breaks
  const lines = processLineBreaks(text);
  
  lines.forEach((line, lineIndex) => {
    if (lineIndex > 0) {
      // Add line break segment between lines
      segments.push({ type: 'linebreak', content: '\n' });
    }
    
    if (line === '') {
      // Empty line - add another line break for paragraph spacing
      segments.push({ type: 'linebreak', content: '\n' });
      return;
    }
    
    // Find all URLs in this line
    const urls = findAllUrls(line);
    
    if (urls.length === 0) {
      // No URLs found, process markdown formatting
      const formattedSegments = processMarkdownFormatting(line);
      segments.push(...formattedSegments);
    } else {
      // Process line with URLs
      let lastIndex = 0;
      
      urls.forEach(({ url, isYouTube, info }) => {
        const urlIndex = line.indexOf(url, lastIndex);
        
        // Add text before the URL (with markdown processing)
        if (urlIndex > lastIndex) {
          const beforeText = line.substring(lastIndex, urlIndex);
          const formattedSegments = processMarkdownFormatting(beforeText);
          segments.push(...formattedSegments);
        }
        
        // Add the URL segment
        if (isYouTube && info) {
          segments.push({
            type: 'youtube',
            content: url,
            info,
            url
          });
        } else {
          segments.push({
            type: 'link',
            content: url,
            url
          });
        }
        
        lastIndex = urlIndex + url.length;
      });
      
      // Add remaining text after the last URL (with markdown processing)
      if (lastIndex < line.length) {
        const remainingText = line.substring(lastIndex);
        const formattedSegments = processMarkdownFormatting(remainingText);
        segments.push(...formattedSegments);
      }
    }
  });
  
  return segments;
}

/**
 * Backward compatibility function that maintains the original YouTube-only parsing
 * This ensures existing code continues to work while new code can use the enhanced parser
 */
export function parseTextWithYouTubeLinks(text: string): Array<{ type: 'text' | 'youtube'; content: string; info?: YouTubeVideoInfo }> {
  const richSegments = parseRichText(text);
  
  // Convert rich segments back to the original format for backward compatibility
  return richSegments.map(segment => {
    if (segment.type === 'youtube') {
      return { type: 'youtube', content: segment.content, info: segment.info };
    } else {
      // Convert all other types back to text for backward compatibility
      return { type: 'text', content: segment.content };
    }
  });
}