// Admin-specific route guards for protecting admin-only routes

import { RouteGuard, RouteGuardResult, RouteConfig, NavigationContext } from '../types/routing';

/**
 * Admin guard - checks if user has admin privileges
 */
export const adminGuard: RouteGuard = {
  name: 'admin',
  check: (route: RouteConfig, context: NavigationContext): RouteGuardResult => {
    // If route doesn't require admin access, allow access
    if (!route.requiresAdmin) {
      return { allowed: true };
    }

    // Check if user is authenticated
    if (!context.isAuthenticated || context.isGuest) {
      return {
        allowed: false,
        redirectTo: '/app/auth',
        reason: 'Authentication required to access admin features'
      };
    }

    // Check if user has admin privileges
    if (!context.isAdmin) {
      return {
        allowed: false,
        redirectTo: '/app/dashboard',
        reason: 'Admin privileges required to access this page'
      };
    }

    // User is authenticated and has admin privileges
    return { allowed: true };
  }
};

/**
 * Admin dashboard guard - specifically for admin dashboard access
 */
export const adminDashboardGuard: RouteGuard = {
  name: 'adminDashboard',
  check: (route: RouteConfig, context: NavigationContext): RouteGuardResult => {
    // Only apply to admin dashboard routes
    if (!route.path.includes('admin-dashboard')) {
      return { allowed: true };
    }

    // Check if user is authenticated
    if (!context.isAuthenticated || context.isGuest) {
      return {
        allowed: false,
        redirectTo: '/app/auth',
        reason: 'Please sign in to access the admin dashboard'
      };
    }

    // Check if user has admin privileges
    if (!context.isAdmin) {
      return {
        allowed: false,
        redirectTo: '/app/dashboard',
        reason: 'Access denied. Admin privileges required.'
      };
    }

    return { allowed: true };
  }
};

/**
 * Utility function to check if current user can access admin features
 */
export const canAccessAdminFeatures = (context: NavigationContext): boolean => {
  return context.isAuthenticated && !context.isGuest && context.isAdmin;
};

/**
 * Utility function to get admin access error message
 */
export const getAdminAccessErrorMessage = (context: NavigationContext): string => {
  if (!context.isAuthenticated || context.isGuest) {
    return 'Please sign in with an admin account to access this feature.';
  }
  
  if (!context.isAdmin) {
    return 'Admin privileges are required to access this feature.';
  }
  
  return 'Access denied.';
};
