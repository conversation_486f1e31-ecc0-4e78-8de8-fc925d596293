import React, { createContext, useContext, useState, useCallback, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';

interface PromotionalContextType {
  showPromotionalNotification: boolean;
  dismissPromotionalNotification: () => void;
  resetPromotionalNotification: () => void;
  canShowPromotion: boolean;
}

const PromotionalContext = createContext<PromotionalContextType | undefined>(undefined);

interface PromotionalProviderProps {
  children: ReactNode;
}

const PROMO_DISMISSED_KEY = 'datastatpro_promo_dismissed';
const PROMO_LAST_SHOWN_KEY = 'datastatpro_promo_last_shown';
const PROMO_SHOW_INTERVAL = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

export const PromotionalProvider: React.FC<PromotionalProviderProps> = ({ children }) => {
  const { user, isGuest } = useAuth();
  const [showPromotionalNotification, setShowPromotionalNotification] = useState(false);
  const [canShowPromotion, setCanShowPromotion] = useState(false);

  // Check if user can see promotional notifications
  useEffect(() => {
    // Only show to authenticated users (not guests)
    if (user && !isGuest) {
      const dismissed = localStorage.getItem(PROMO_DISMISSED_KEY);
      const lastShown = localStorage.getItem(PROMO_LAST_SHOWN_KEY);
      
      if (!dismissed) {
        // If never dismissed, check if enough time has passed since last shown
        if (!lastShown) {
          // Never shown before, show it
          setCanShowPromotion(true);
          setShowPromotionalNotification(true);
        } else {
          const lastShownTime = parseInt(lastShown, 10);
          const now = Date.now();
          
          if (now - lastShownTime > PROMO_SHOW_INTERVAL) {
            // Enough time has passed, show it again
            setCanShowPromotion(true);
            setShowPromotionalNotification(true);
          }
        }
      }
    } else {
      // Not authenticated or is guest, don't show
      setCanShowPromotion(false);
      setShowPromotionalNotification(false);
    }
  }, [user, isGuest]);

  const dismissPromotionalNotification = useCallback(() => {
    setShowPromotionalNotification(false);
    // Mark as dismissed permanently
    localStorage.setItem(PROMO_DISMISSED_KEY, 'true');
    localStorage.setItem(PROMO_LAST_SHOWN_KEY, Date.now().toString());
  }, []);

  const resetPromotionalNotification = useCallback(() => {
    // For admin/testing purposes - reset the promotional notification state
    localStorage.removeItem(PROMO_DISMISSED_KEY);
    localStorage.removeItem(PROMO_LAST_SHOWN_KEY);
    if (user && !isGuest) {
      setCanShowPromotion(true);
      setShowPromotionalNotification(true);
    }
  }, [user, isGuest]);

  const value: PromotionalContextType = {
    showPromotionalNotification,
    dismissPromotionalNotification,
    resetPromotionalNotification,
    canShowPromotion
  };

  return (
    <PromotionalContext.Provider value={value}>
      {children}
    </PromotionalContext.Provider>
  );
};

export const usePromotional = (): PromotionalContextType => {
  const context = useContext(PromotionalContext);
  if (context === undefined) {
    throw new Error('usePromotional must be used within a PromotionalProvider');
  }
  return context;
};