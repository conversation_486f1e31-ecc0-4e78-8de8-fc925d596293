import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Alert,
  Paper,
  useTheme,
  CircularProgress,
  Divider
} from '@mui/material';
import { Email as EmailIcon, Verified as VerifiedIcon } from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import { GuestUserStatus } from '../../types';

interface EmailVerifiedGuestAccessProps {
  onVerificationSent?: () => void;
  onLoginSuccess?: () => void;
}

const EmailVerifiedGuestAccess: React.FC<EmailVerifiedGuestAccessProps> = ({
  onVerificationSent,
  onLoginSuccess
}) => {
  const theme = useTheme();
  const { createGuestUser, verifyGuestEmail, loginAsEmailVerifiedGuest, guestUser } = useAuth();
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [step, setStep] = useState<'email' | 'verification'>('email');

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);
    setLoading(true);

    try {
      const response = await createGuestUser({ email });
      if (response.success) {
        setSuccess('Verification code sent to your email!');
        setStep('verification');
        onVerificationSent?.();
      } else {
        setError(response.error || 'Failed to send verification code');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to send verification code');
    } finally {
      setLoading(false);
    }
  };

  const handleVerificationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);
    setLoading(true);

    try {
      const verifyResponse = await verifyGuestEmail({ email, verification_code: verificationCode });
      if (verifyResponse.success) {
        const loginResponse = await loginAsEmailVerifiedGuest({ email });
        if (loginResponse.success) {
          setSuccess('Email verified successfully! Welcome to DataStatPro.');
          onLoginSuccess?.();
        } else {
          setError(loginResponse.error || 'Failed to login after verification');
        }
      } else {
        setError(verifyResponse.error || 'Invalid verification code');
      }
    } catch (err: any) {
      setError(err.message || 'Verification failed');
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    setError(null);
    setSuccess(null);
    setLoading(true);

    try {
      const response = await createGuestUser({ email });
      if (response.success) {
        setSuccess('New verification code sent!');
      } else {
        setError(response.error || 'Failed to resend verification code');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to resend verification code');
    } finally {
      setLoading(false);
    }
  };

  const handleBackToEmail = () => {
    setStep('email');
    setVerificationCode('');
    setError(null);
    setSuccess(null);
  };

  return (
    <Paper elevation={3} sx={{ p: 4, maxWidth: 450, mx: 'auto', mt: 4 }}>      
      <Box display="flex" flexDirection="column" alignItems="center" mb={3}>
        <VerifiedIcon fontSize="large" color="primary" sx={{ mb: 1 }} />
        <Typography variant="h5" component="h1" gutterBottom>
          Guest Access
        </Typography>
      </Box>

      <Alert 
        severity="info" 
        sx={{ 
          mb: 3, 
          border: `1px solid ${theme.palette.info.main}`,
          '& .MuiAlert-message': { width: '100%' }
        }}
      >
        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
          Guest Access Information
        </Typography>
        <Typography variant="body2" paragraph>
          Guest users may explore the application with all features but are limited to Sample Datasets for teaching and learning purposes.
        </Typography>
        <Typography variant="body2" component="ul" sx={{ pl: 2, m: 0 }}>
          <li>Access to all statistical analysis features</li>
          <li>Limited to pre-loaded sample datasets only</li>
          <li>No data saving or custom data importing</li>
          <li>Perfect for learning and evaluation</li>
        </Typography>
      </Alert>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      <Typography variant="body2" paragraph textAlign="center" sx={{ mb: 3 }}>
        Email verification required for enhanced security. Start exploring DataStatPro with sample datasets.
      </Typography>

      {step === 'email' && (
        <form onSubmit={handleEmailSubmit}>
          <TextField
            fullWidth
            type="email"
            label="Email Address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            disabled={loading}
            InputProps={{
              startAdornment: <EmailIcon sx={{ mr: 1, color: 'action.active' }} />
            }}
            sx={{ mb: 3 }}
            helperText="We'll send you a verification code to confirm your email"
          />

          <Button
            fullWidth
            type="submit"
            variant="contained"
            color="primary"
            size="large"
            disabled={loading || !email}
            startIcon={loading ? <CircularProgress size={20} /> : <EmailIcon />}
            sx={{ 
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 'medium'
            }}
          >
            {loading ? 'Sending...' : 'Send Verification Code'}
          </Button>
        </form>
      )}

      {step === 'verification' && (
        <>
          <Typography variant="body2" textAlign="center" sx={{ mb: 3 }}>
            We've sent a verification code to <strong>{email}</strong>
          </Typography>

          <form onSubmit={handleVerificationSubmit}>
            <TextField
              fullWidth
              label="Verification Code"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              required
              disabled={loading}
              placeholder="Enter 6-digit code"
              inputProps={{ maxLength: 6 }}
              sx={{ mb: 3 }}
              helperText="Check your email for the verification code"
            />

            <Button
              fullWidth
              type="submit"
              variant="contained"
              color="primary"
              size="large"
              disabled={loading || verificationCode.length !== 6}
              startIcon={loading ? <CircularProgress size={20} /> : <VerifiedIcon />}
              sx={{ 
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 'medium',
                mb: 2
              }}
            >
              {loading ? 'Verifying...' : 'Verify & Continue'}
            </Button>
          </form>

          <Divider sx={{ my: 2 }} />

          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Button
              variant="text"
              onClick={handleBackToEmail}
              disabled={loading}
              sx={{ textTransform: 'none' }}
            >
              ← Change Email
            </Button>
            
            <Button
              variant="text"
              onClick={handleResendCode}
              disabled={loading}
              sx={{ textTransform: 'none' }}
            >
              Resend Code
            </Button>
          </Box>
        </>
      )}

      <Typography variant="caption" display="block" textAlign="center" sx={{ mt: 3, color: 'text.secondary' }}>
        Your email is only used for verification and session management. We don't store personal data.
      </Typography>
    </Paper>
  );
};

export default EmailVerifiedGuestAccess;