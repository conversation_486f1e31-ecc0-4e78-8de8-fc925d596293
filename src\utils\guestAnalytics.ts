import { supabase } from './supabaseClient';
import { GuestAnalytics } from '../types';

/**
 * Guest Analytics Tracking Utilities
 * Handles tracking of guest user sessions and activities
 */

export interface GuestAnalyticsEvent {
  event_type: 'page_view' | 'feature_usage' | 'data_interaction' | 'export_action' | 'session_end';
  event_data?: Record<string, any>;
  timestamp?: string;
}

export interface GuestSessionMetrics {
  session_duration: number;
  pages_visited: number;
  features_used: string[];
  data_interactions: number;
  exports_performed: number;
}

/**
 * Start a new guest analytics session
 */
export const startGuestAnalyticsSession = async (
  guestUserId: string,
  userAgent?: string
): Promise<GuestAnalytics | null> => {
  try {
    const sessionData = {
      guest_user_id: guestUserId,
      session_start: new Date().toISOString(),
      user_agent: userAgent || navigator.userAgent,
      ip_address: null, // Will be populated by database trigger if needed
      session_data: {
        pages_visited: 0,
        features_used: [],
        data_interactions: 0,
        exports_performed: 0
      }
    };

    const { data, error } = await supabase
      .from('guest_analytics')
      .insert([sessionData])
      .select()
      .single();

    if (error) {
      console.error('Error starting guest analytics session:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error starting guest analytics session:', error);
    return null;
  }
};

/**
 * End a guest analytics session
 */
export const endGuestAnalyticsSession = async (
  sessionId: string,
  metrics?: Partial<GuestSessionMetrics>
): Promise<boolean> => {
  try {
    const updateData: any = {
      session_end: new Date().toISOString()
    };

    if (metrics) {
      updateData.session_data = metrics;
    }

    const { error } = await supabase
      .from('guest_analytics')
      .update(updateData)
      .eq('id', sessionId);

    if (error) {
      console.error('Error ending guest analytics session:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error ending guest analytics session:', error);
    return false;
  }
};

/**
 * Track a guest analytics event
 */
export const trackGuestEvent = async (
  sessionId: string,
  event: GuestAnalyticsEvent
): Promise<boolean> => {
  try {
    // First, get the current session data
    const { data: session, error: fetchError } = await supabase
      .from('guest_analytics')
      .select('session_data')
      .eq('id', sessionId)
      .single();

    if (fetchError) {
      console.error('Error fetching session data:', fetchError);
      return false;
    }

    // Update session metrics based on event type
    const currentData = session.session_data || {
      pages_visited: 0,
      features_used: [],
      data_interactions: 0,
      exports_performed: 0
    };

    switch (event.event_type) {
      case 'page_view':
        currentData.pages_visited = (currentData.pages_visited || 0) + 1;
        break;
      case 'feature_usage':
        if (event.event_data?.feature && !currentData.features_used.includes(event.event_data.feature)) {
          currentData.features_used.push(event.event_data.feature);
        }
        break;
      case 'data_interaction':
        currentData.data_interactions = (currentData.data_interactions || 0) + 1;
        break;
      case 'export_action':
        currentData.exports_performed = (currentData.exports_performed || 0) + 1;
        break;
    }

    // Update the session with new metrics
    const { error: updateError } = await supabase
      .from('guest_analytics')
      .update({ session_data: currentData })
      .eq('id', sessionId);

    if (updateError) {
      console.error('Error updating session metrics:', updateError);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error tracking guest event:', error);
    return false;
  }
};

/**
 * Get guest analytics summary for a user
 */
export const getGuestAnalyticsSummary = async (
  guestUserId: string
): Promise<{
  total_sessions: number;
  total_session_time: number;
  avg_session_time: number;
  total_page_views: number;
  unique_features_used: string[];
  total_data_interactions: number;
  total_exports: number;
} | null> => {
  try {
    const { data: sessions, error } = await supabase
      .from('guest_analytics')
      .select('*')
      .eq('guest_user_id', guestUserId)
      .not('session_end', 'is', null); // Only completed sessions

    if (error) {
      console.error('Error fetching guest analytics:', error);
      return null;
    }

    if (!sessions || sessions.length === 0) {
      return {
        total_sessions: 0,
        total_session_time: 0,
        avg_session_time: 0,
        total_page_views: 0,
        unique_features_used: [],
        total_data_interactions: 0,
        total_exports: 0
      };
    }

    let totalSessionTime = 0;
    let totalPageViews = 0;
    let allFeaturesUsed = new Set<string>();
    let totalDataInteractions = 0;
    let totalExports = 0;

    sessions.forEach(session => {
      // Calculate session duration
      if (session.session_start && session.session_end) {
        const start = new Date(session.session_start).getTime();
        const end = new Date(session.session_end).getTime();
        totalSessionTime += (end - start) / 1000; // Convert to seconds
      }

      // Aggregate session data
      const sessionData = session.session_data || {};
      totalPageViews += sessionData.pages_visited || 0;
      totalDataInteractions += sessionData.data_interactions || 0;
      totalExports += sessionData.exports_performed || 0;

      // Collect unique features
      if (sessionData.features_used && Array.isArray(sessionData.features_used)) {
        sessionData.features_used.forEach((feature: string) => allFeaturesUsed.add(feature));
      }
    });

    return {
      total_sessions: sessions.length,
      total_session_time: totalSessionTime,
      avg_session_time: sessions.length > 0 ? totalSessionTime / sessions.length : 0,
      total_page_views: totalPageViews,
      unique_features_used: Array.from(allFeaturesUsed),
      total_data_interactions: totalDataInteractions,
      total_exports: totalExports
    };
  } catch (error) {
    console.error('Error getting guest analytics summary:', error);
    return null;
  }
};

/**
 * Clean up old guest analytics data (older than specified days)
 */
export const cleanupOldGuestAnalytics = async (daysToKeep: number = 30): Promise<boolean> => {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const { error } = await supabase
      .from('guest_analytics')
      .delete()
      .lt('session_start', cutoffDate.toISOString());

    if (error) {
      console.error('Error cleaning up old guest analytics:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error cleaning up old guest analytics:', error);
    return false;
  }
};