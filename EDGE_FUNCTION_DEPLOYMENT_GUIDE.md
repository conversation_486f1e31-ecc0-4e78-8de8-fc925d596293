# Edge Function Deployment Guide

This guide provides multiple methods to deploy the `send-guest-verification-email` Edge Function to Supabase.

## Method 1: Using Supabase CLI (Recommended)

### Prerequisites
- Supabase CLI is already available via npx
- Supabase account with project access
- Project linked to Supabase

### Step 1: Login to Supabase CLI
```bash
npx supabase login
```
This will open a browser window for authentication. Follow the prompts to login.

### Step 2: Link Your Project
```bash
npx supabase link --project-ref YOUR_PROJECT_REF
```
Replace `YOUR_PROJECT_REF` with your actual Supabase project reference ID (found in your Supabase Dashboard URL).

### Step 3: Deploy the Edge Function
```bash
npx supabase functions deploy send-guest-verification-email
```

### Step 4: Set Environment Variables
```bash
npx supabase secrets set RESEND_API_KEY=your_resend_api_key_here
npx supabase secrets set FROM_EMAIL=<EMAIL>
```

## Method 2: Manual Deployment via Supabase Dashboard

### Step 1: Access Supabase Dashboard
1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Login to your account
3. Select your DataStatPro project

### Step 2: Navigate to Edge Functions
1. In the left sidebar, click on "Edge Functions"
2. Click "Create a new function"
3. Name it: `send-guest-verification-email`

### Step 3: Copy Function Code
Copy the entire content from `supabase/functions/send-guest-verification-email/index.ts` and paste it into the function editor.

### Step 4: Set Environment Variables
1. Go to "Settings" → "Environment Variables"
2. Add the following variables:
   - `RESEND_API_KEY`: Your Resend API key
   - `FROM_EMAIL`: `<EMAIL>`

### Step 5: Deploy
Click "Deploy function" to deploy your Edge Function.

## Method 3: Using Supabase Management API

### Prerequisites
- Supabase Service Role Key
- HTTP client (curl, Postman, etc.)

### Step 1: Get Your Service Role Key
1. Go to Supabase Dashboard → Settings → API
2. Copy the "service_role" key (keep it secure!)

### Step 2: Deploy via API
```bash
curl -X POST 'https://api.supabase.com/v1/projects/YOUR_PROJECT_REF/functions' \
  -H 'Authorization: Bearer YOUR_SERVICE_ROLE_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "slug": "send-guest-verification-email",
    "name": "send-guest-verification-email",
    "source_code": "BASE64_ENCODED_FUNCTION_CODE",
    "verify_jwt": false
  }'
```

## Getting Resend API Key

1. Go to [https://resend.com](https://resend.com)
2. Sign up or login to your account
3. Navigate to "API Keys" in the dashboard
4. Click "Create API Key"
5. Give it a name (e.g., "DataStatPro Email Verification")
6. Select appropriate permissions (Send emails)
7. Copy the generated API key

## Verification Steps

### Test the Edge Function
1. Go to Supabase Dashboard → Edge Functions
2. Find your `send-guest-verification-email` function
3. Click "Invoke" to test it
4. Use this test payload:
```json
{
  "email": "<EMAIL>",
  "verificationCode": "123456"
}
```

### Check Environment Variables
1. In Supabase Dashboard → Settings → Environment Variables
2. Verify that `RESEND_API_KEY` and `FROM_EMAIL` are set correctly

### Test Email Delivery
1. Try the guest email verification flow in your application
2. Check if emails are being delivered
3. Monitor the Edge Function logs for any errors

## Troubleshooting

### Common Issues

1. **"Access token not provided"**
   - Run `npx supabase login` to authenticate

2. **"Project not linked"**
   - Run `npx supabase link --project-ref YOUR_PROJECT_REF`

3. **"Function deployment failed"**
   - Check function syntax and dependencies
   - Verify you have the correct permissions

4. **"Emails not being sent"**
   - Verify Resend API key is correct
   - Check FROM_EMAIL domain is verified in Resend
   - Monitor Edge Function logs for errors

5. **"Permission denied"**
   - Ensure you have admin access to the Supabase project
   - Check if your account has the necessary permissions

### Getting Help

- Supabase Documentation: [https://supabase.com/docs](https://supabase.com/docs)
- Supabase Discord: [https://discord.supabase.com](https://discord.supabase.com)
- Resend Documentation: [https://resend.com/docs](https://resend.com/docs)

## Next Steps

After successful deployment:
1. Test the email verification flow
2. Monitor email delivery rates
3. Set up proper error handling and logging
4. Consider implementing email templates for better branding

---

**Note**: The Edge Function has been updated to use `<EMAIL>` as the sender email address. Make sure this domain is verified in your Resend account.