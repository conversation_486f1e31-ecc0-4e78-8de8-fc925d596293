// Main application router component

import React, { Suspense, useEffect } from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useData } from '../context/DataContext';
import { useAuth } from '../context/AuthContext';
import { RouteConfig, NavigationContext } from '../types/routing';
import { applyRouteGuards } from './RouteGuards';
import { routeRegistry, findRoute } from './RouteRegistry';
import { initializeRoutes } from './routeConfig';

interface AppRouterProps {
  activePage: string;
  activeSubPage: string;
  onNavigate: (path: string) => void;
}

// Loading component for Suspense
const RouteLoader: React.FC<{ message?: string }> = ({ message = 'Loading...' }) => (
  <Box
    display="flex"
    flexDirection="column"
    justifyContent="center"
    alignItems="center"
    minHeight="400px"
    gap={2}
  >
    <CircularProgress size={40} />
    <Typography variant="body2" color="text.secondary">
      {message}
    </Typography>
  </Box>
);

// Error boundary for route components
class RouteErrorBoundary extends React.Component<
  { children: React.ReactNode; routePath: string },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode; routePath: string }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error(`Route error in ${this.props.routePath}:`, error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Box p={3} textAlign="center">
          <Typography variant="h6" color="error" gutterBottom>
            Something went wrong loading this page
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Route: {this.props.routePath}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            {this.state.error?.message}
          </Typography>
        </Box>
      );
    }

    return this.props.children;
  }
}

export const AppRouter: React.FC<AppRouterProps> = ({
  activePage,
  activeSubPage,
  onNavigate
}) => {
  const dataContext = useData();
  const authContext = useAuth();

  // Routes are now initialized in App.tsx to prevent race conditions

  // Create navigation context
  const navigationContext: NavigationContext = {
    navigateToPage: onNavigate,
    currentPage: activePage,
    currentSubPage: activeSubPage,
    isAuthenticated: authContext.isAuthenticated || false,
    isGuest: authContext.isGuest || false,
    isAdmin: authContext.isAdmin || false, // Add admin status
    user: authContext.user || null
  };





  // Find matching route
  const route = findRoute(activePage, activeSubPage);

  // Debug logging for route resolution (only in development)
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 Route Resolution Debug:', {
      activePage,
      activeSubPage,
      foundRoute: route?.path,
      routeComponent: route?.component?.name
    });
    
    // Additional debug logging for advanced-analysis routes
    if (activePage === 'advanced-analysis') {
      console.log('🔍 Advanced Analysis Route Debug:', {
        activePage,
        activeSubPage,
        foundRoute: route?.path,
        routeComponent: route?.component?.name
      });
    }
  }

  if (!route) {
    return (
      <Box p={3} textAlign="center">
        <Typography variant="h6" color="error" gutterBottom>
          Page Not Found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          The page "{activePage}{activeSubPage ? `/${activeSubPage}` : ''}" could not be found.
        </Typography>
      </Box>
    );
  }

  // Apply route guards
  const guardResult = applyRouteGuards(route, navigationContext);

  if (!guardResult.allowed) {
    if (guardResult.redirectTo) {
      // Trigger redirect
      setTimeout(() => onNavigate(guardResult.redirectTo!), 0);
      return (
        <RouteLoader message={`Redirecting... ${guardResult.reason || ''}`} />
      );
    }
    
    return (
      <Box p={3} textAlign="center">
        <Typography variant="h6" color="warning.main" gutterBottom>
          Access Denied
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {guardResult.reason || 'You do not have permission to access this page.'}
        </Typography>
      </Box>
    );
  }

  // Render the route component
  const Component = route.component;
  const routeProps = {
    onNavigate,
    initialTab: activeSubPage,
    initialSubPage: activeSubPage,
    ...route.props
  };

  // Special handling for Auth component - provide onAuthSuccess callback
  if (route.path === 'auth' || route.path.startsWith('auth/')) {
    routeProps.onAuthSuccess = () => {
      onNavigate('/app/dashboard');
    };
  }

  const routePath = `${activePage}${activeSubPage ? `/${activeSubPage}` : ''}`;

  return (
    <RouteErrorBoundary routePath={routePath}>
      <Suspense fallback={<RouteLoader message="Loading page..." />}>
        <Component {...routeProps} />
      </Suspense>
    </RouteErrorBoundary>
  );
};

export default AppRouter;
