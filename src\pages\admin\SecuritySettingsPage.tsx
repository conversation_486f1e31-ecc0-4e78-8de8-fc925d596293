import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Switch,
  FormControlLabel,
  TextField,
  Button,
  IconButton,
  Tooltip,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Divider,
  Alert,
  AlertTitle,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  Tabs,
  Tab
} from '@mui/material';
import {
  ArrowBack,
  FullscreenExit,
  Fullscreen as FullscreenIcon,
  Security,
  Shield,
  Lock,
  Key,
  Warning,
  CheckCircle,
  Error,
  VpnKey,
  Fingerprint,
  AdminPanelSettings,
  Block,
  Visibility,
  VisibilityOff,
  Delete,
  Add,
  Edit,
  History,
  LocationOn,
  Computer,
  Smartphone,
  Tablet,
  Refresh
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import PageTitle from '../../components/UI/PageTitle';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`security-tabpanel-${index}`}
      aria-labelledby={`security-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface SecuritySettings {
  twoFactorAuth: boolean;
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
    expirationDays: number;
  };
  sessionTimeout: number;
  ipWhitelist: string[];
  failedLoginAttempts: number;
  accountLockoutDuration: number;
  auditLogging: boolean;
  encryptionEnabled: boolean;
  sslRequired: boolean;
}

interface ActiveSession {
  id: string;
  user: string;
  device: string;
  location: string;
  loginTime: string;
  lastActivity: string;
  ipAddress: string;
  deviceType: 'desktop' | 'mobile' | 'tablet';
}

interface SecurityAlert {
  id: string;
  type: 'warning' | 'error' | 'info';
  title: string;
  description: string;
  timestamp: string;
  resolved: boolean;
}

const SecuritySettingsPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const [isMaximized, setIsMaximized] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState<'session' | 'ip' | 'alert'>('session');
  const [selectedItem, setSelectedItem] = useState<any>(null);

  const handleRefresh = () => {
    setIsLoading(true);
    // Simulate refresh
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  const [settings, setSettings] = useState<SecuritySettings>({
    twoFactorAuth: true,
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      expirationDays: 90
    },
    sessionTimeout: 30,
    ipWhitelist: ['***********/24', '10.0.0.0/8'],
    failedLoginAttempts: 5,
    accountLockoutDuration: 15,
    auditLogging: true,
    encryptionEnabled: true,
    sslRequired: true
  });

  const [activeSessions] = useState<ActiveSession[]>([
    {
      id: '1',
      user: 'John Smith',
      device: 'Chrome on Windows',
      location: 'New York, US',
      loginTime: '2024-01-15 09:30:00',
      lastActivity: '2 minutes ago',
      ipAddress: '*************',
      deviceType: 'desktop'
    },
    {
      id: '2',
      user: 'Sarah Johnson',
      device: 'Safari on iPhone',
      location: 'London, UK',
      loginTime: '2024-01-15 08:45:00',
      lastActivity: '5 minutes ago',
      ipAddress: '*********',
      deviceType: 'mobile'
    },
    {
      id: '3',
      user: 'Mike Davis',
      device: 'Firefox on Linux',
      location: 'Toronto, CA',
      loginTime: '2024-01-15 07:15:00',
      lastActivity: '1 hour ago',
      ipAddress: '***********',
      deviceType: 'desktop'
    }
  ]);

  const [securityAlerts] = useState<SecurityAlert[]>([
    {
      id: '1',
      type: 'warning',
      title: 'Multiple Failed Login Attempts',
      description: 'User account "<EMAIL>" has 4 failed login attempts in the last hour.',
      timestamp: '2024-01-15 10:30:00',
      resolved: false
    },
    {
      id: '2',
      type: 'error',
      title: 'Suspicious IP Access',
      description: 'Login attempt from unrecognized IP address: ************',
      timestamp: '2024-01-15 09:45:00',
      resolved: false
    },
    {
      id: '3',
      type: 'info',
      title: 'Password Policy Updated',
      description: 'Password complexity requirements have been updated successfully.',
      timestamp: '2024-01-15 08:20:00',
      resolved: true
    }
  ]);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleToggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  const handleBackToAdmin = () => {
    navigate('/app/admin-dashboard');
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSettingChange = (setting: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [setting]: value
    }));
  };

  const handlePasswordPolicyChange = (field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      passwordPolicy: {
        ...prev.passwordPolicy,
        [field]: value
      }
    }));
  };

  const handleSaveSettings = () => {
    // Here you would save settings to the backend
    console.log('Saving security settings:', settings);
    // Show success message
  };

  const handleResetSettings = () => {
    // Reset to default values
    setSettings({
      twoFactorAuth: true,
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
        expirationDays: 90
      },
      sessionTimeout: 30,
      ipWhitelist: ['***********/24', '10.0.0.0/8'],
      failedLoginAttempts: 5,
      accountLockoutDuration: 15,
      auditLogging: true,
      encryptionEnabled: true,
      sslRequired: true
    });
  };

  const handleTerminateSession = (sessionId: string) => {
    console.log('Terminating session:', sessionId);
    // Here you would terminate the session
  };

  const handleResolveAlert = (alertId: string) => {
    console.log('Resolving alert:', alertId);
    // Here you would mark the alert as resolved
  };

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile':
        return <Smartphone />;
      case 'tablet':
        return <Tablet />;
      default:
        return <Computer />;
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <Error color="error" />;
      case 'warning':
        return <Warning color="warning" />;
      default:
        return <CheckCircle color="info" />;
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: 'background.default',
        transition: 'all 0.3s ease-in-out',
        ...(isMaximized && {
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 9999,
          bgcolor: 'background.paper'
        })
      }}
    >
      <Container
        maxWidth={isMaximized ? false : 'xl'}
        sx={{
          py: 3,
          px: isMaximized ? 3 : undefined,
          height: isMaximized ? '100vh' : 'auto',
          overflow: isMaximized ? 'auto' : 'visible'
        }}
      >
        <PageTitle
          title="Security Settings"
          description="Configure security policies and monitor system security"
          icon={<Security />}
          breadcrumbs={[
            {
              label: 'Admin Dashboard',
              onClick: handleBackToAdmin
            },
            {
              label: 'Security Settings'
            }
          ]}
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Refresh Settings">
                <IconButton
                  onClick={handleRefresh}
                  sx={{
                    bgcolor: 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                >
                  <Refresh />
                </IconButton>
              </Tooltip>
              <Tooltip title={isMaximized ? 'Minimize' : 'Maximize'}>
                <IconButton
                  onClick={handleToggleMaximize}
                  sx={{
                    bgcolor: 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                >
                  {isMaximized ? <FullscreenExit /> : <FullscreenIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          }
        />

        {/* Security Status Alert */}
        <Alert severity="success" sx={{ mb: 3 }}>
          <AlertTitle>Security Status: Good</AlertTitle>
          All critical security measures are active and functioning properly.
        </Alert>

        {/* Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab icon={<Shield />} label="Authentication" />
            <Tab icon={<Lock />} label="Password Policy" />
            <Tab icon={<AdminPanelSettings />} label="Access Control" />
            <Tab icon={<History />} label="Active Sessions" />
            <Tab icon={<Warning />} label="Security Alerts" />
          </Tabs>

          {/* Authentication Tab */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardHeader title="Two-Factor Authentication" avatar={<Fingerprint />} />
                  <CardContent>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.twoFactorAuth}
                          onChange={(e) => handleSettingChange('twoFactorAuth', e.target.checked)}
                        />
                      }
                      label="Enable Two-Factor Authentication"
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Require users to provide a second form of authentication
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardHeader title="Session Management" avatar={<VpnKey />} />
                  <CardContent>
                    <TextField
                      fullWidth
                      label="Session Timeout (minutes)"
                      type="number"
                      value={settings.sessionTimeout}
                      onChange={(e) => handleSettingChange('sessionTimeout', parseInt(e.target.value))}
                      sx={{ mb: 2 }}
                    />
                    <Typography variant="body2" color="text.secondary">
                      Automatically log out inactive users after specified time
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12}>
                <Card>
                  <CardHeader title="Security Features" avatar={<Security />} />
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={settings.auditLogging}
                              onChange={(e) => handleSettingChange('auditLogging', e.target.checked)}
                            />
                          }
                          label="Audit Logging"
                        />
                        <Typography variant="body2" color="text.secondary">
                          Log all security-related events
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={settings.encryptionEnabled}
                              onChange={(e) => handleSettingChange('encryptionEnabled', e.target.checked)}
                            />
                          }
                          label="Data Encryption"
                        />
                        <Typography variant="body2" color="text.secondary">
                          Encrypt sensitive data at rest
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={settings.sslRequired}
                              onChange={(e) => handleSettingChange('sslRequired', e.target.checked)}
                            />
                          }
                          label="Require SSL/TLS"
                        />
                        <Typography variant="body2" color="text.secondary">
                          Force HTTPS connections
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Password Policy Tab */}
          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardHeader title="Password Requirements" avatar={<Key />} />
                  <CardContent>
                    <TextField
                      fullWidth
                      label="Minimum Length"
                      type="number"
                      value={settings.passwordPolicy.minLength}
                      onChange={(e) => handlePasswordPolicyChange('minLength', parseInt(e.target.value))}
                      sx={{ mb: 2 }}
                    />
                    <TextField
                      fullWidth
                      label="Password Expiration (days)"
                      type="number"
                      value={settings.passwordPolicy.expirationDays}
                      onChange={(e) => handlePasswordPolicyChange('expirationDays', parseInt(e.target.value))}
                      sx={{ mb: 2 }}
                    />
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardHeader title="Complexity Requirements" avatar={<Lock />} />
                  <CardContent>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.passwordPolicy.requireUppercase}
                          onChange={(e) => handlePasswordPolicyChange('requireUppercase', e.target.checked)}
                        />
                      }
                      label="Require Uppercase Letters"
                      sx={{ display: 'block', mb: 1 }}
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.passwordPolicy.requireNumbers}
                          onChange={(e) => handlePasswordPolicyChange('requireNumbers', e.target.checked)}
                        />
                      }
                      label="Require Numbers"
                      sx={{ display: 'block', mb: 1 }}
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.passwordPolicy.requireSpecialChars}
                          onChange={(e) => handlePasswordPolicyChange('requireSpecialChars', e.target.checked)}
                        />
                      }
                      label="Require Special Characters"
                      sx={{ display: 'block' }}
                    />
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Access Control Tab */}
          <TabPanel value={tabValue} index={2}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardHeader title="Login Protection" avatar={<Block />} />
                  <CardContent>
                    <TextField
                      fullWidth
                      label="Failed Login Attempts Limit"
                      type="number"
                      value={settings.failedLoginAttempts}
                      onChange={(e) => handleSettingChange('failedLoginAttempts', parseInt(e.target.value))}
                      sx={{ mb: 2 }}
                    />
                    <TextField
                      fullWidth
                      label="Account Lockout Duration (minutes)"
                      type="number"
                      value={settings.accountLockoutDuration}
                      onChange={(e) => handleSettingChange('accountLockoutDuration', parseInt(e.target.value))}
                    />
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardHeader title="IP Whitelist" avatar={<LocationOn />} />
                  <CardContent>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Allowed IP addresses and ranges:
                    </Typography>
                    {settings.ipWhitelist.map((ip, index) => (
                      <Chip
                        key={index}
                        label={ip}
                        onDelete={() => {
                          const newList = settings.ipWhitelist.filter((_, i) => i !== index);
                          handleSettingChange('ipWhitelist', newList);
                        }}
                        sx={{ mr: 1, mb: 1 }}
                      />
                    ))}
                    <Button
                      startIcon={<Add />}
                      variant="outlined"
                      size="small"
                      sx={{ mt: 1 }}
                      onClick={() => {
                        // Open dialog to add new IP
                      }}
                    >
                      Add IP Range
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Active Sessions Tab */}
          <TabPanel value={tabValue} index={3}>
            <Card>
              <CardHeader
                title="Active User Sessions"
                avatar={<History />}
                subheader={`${activeSessions.length} active sessions`}
              />
              <CardContent>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>User</TableCell>
                        <TableCell>Device</TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell>Login Time</TableCell>
                        <TableCell>Last Activity</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {activeSessions.map((session) => (
                        <TableRow key={session.id} hover>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Avatar sx={{ width: 32, height: 32 }}>
                                {session.user.charAt(0)}
                              </Avatar>
                              <Typography variant="subtitle2">
                                {session.user}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {getDeviceIcon(session.deviceType)}
                              <Box>
                                <Typography variant="body2">
                                  {session.device}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {session.ipAddress}
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {session.location}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {session.loginTime}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" color="success.main">
                              {session.lastActivity}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Button
                              size="small"
                              color="error"
                              onClick={() => handleTerminateSession(session.id)}
                            >
                              Terminate
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </TabPanel>

          {/* Security Alerts Tab */}
          <TabPanel value={tabValue} index={4}>
            <Card>
              <CardHeader
                title="Security Alerts"
                avatar={<Warning />}
                subheader="Recent security events and notifications"
              />
              <CardContent>
                <List>
                  {securityAlerts.map((alert, index) => (
                    <React.Fragment key={alert.id}>
                      <ListItem alignItems="flex-start">
                        <ListItemIcon>
                          {getAlertIcon(alert.type)}
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="subtitle2">
                                {alert.title}
                              </Typography>
                              {alert.resolved && (
                                <Chip size="small" label="Resolved" color="success" />
                              )}
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2" color="text.secondary">
                                {alert.description}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {alert.timestamp}
                              </Typography>
                            </Box>
                          }
                        />
                        <ListItemSecondaryAction>
                          {!alert.resolved && (
                            <Button
                              size="small"
                              onClick={() => handleResolveAlert(alert.id)}
                            >
                              Resolve
                            </Button>
                          )}
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < securityAlerts.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </TabPanel>
        </Paper>

        {/* Action Buttons */}
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
          <Button variant="outlined" onClick={handleResetSettings}>
            Reset to Defaults
          </Button>
          <Button variant="contained" onClick={handleSaveSettings}>
            Save Security Settings
          </Button>
        </Box>
      </Container>
    </Box>
  );
};

export default SecuritySettingsPage;