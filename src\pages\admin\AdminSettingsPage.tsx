import React, { useState, useContext } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Tabs,
  Tab,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Alert,
  IconButton,
  Tooltip,
  Grid,
  Card,
  CardContent,
  Chip
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Storage as StorageIcon,
  Email as EmailIcon,
  Backup as BackupIcon,
  Tune as GeneralIcon,
  Update as UpdateIcon
} from '@mui/icons-material';
import { AuthContext } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import PageTitle from '../../components/UI/PageTitle';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface AdminSettingsPageProps {}

const AdminSettingsPage: React.FC<AdminSettingsPageProps> = () => {
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const [isMaximized, setIsMaximized] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [settings, setSettings] = useState({
    general: {
      siteName: 'DataStatPro',
      siteDescription: 'Advanced Statistical Analysis Platform',
      maintenanceMode: false,
      registrationEnabled: true,
      emailVerificationRequired: true
    },
    security: {
      passwordMinLength: 8,
      requireSpecialChars: true,
      sessionTimeout: 30,
      maxLoginAttempts: 5,
      twoFactorEnabled: false
    },
    notifications: {
      emailNotifications: true,
      systemAlerts: true,
      userRegistrations: true,
      quotaWarnings: true,
      backupReports: true
    },
    storage: {
      maxFileSize: 100,
      allowedFileTypes: ['csv', 'xlsx', 'json', 'txt'],
      autoCleanup: true,
      cleanupDays: 30
    },
    email: {
      smtpHost: 'smtp.example.com',
      smtpPort: 587,
      smtpUser: '<EMAIL>',
      smtpPassword: '••••••••',
      fromEmail: '<EMAIL>',
      fromName: 'DataStatPro'
    },
    backup: {
      autoBackup: true,
      backupFrequency: 'daily',
      retentionDays: 30,
      backupLocation: 's3://datastatpro-backups'
    }
  });
  const [hasChanges, setHasChanges] = useState(false);

  const handleToggleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  const handleBackToAdmin = () => {
    navigate('/app/admin-dashboard');
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleSettingChange = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const handleSaveSettings = () => {
    // Here you would typically save to your backend
    console.log('Saving settings:', settings);
    setHasChanges(false);
    // Show success message
  };

  const handleResetSettings = () => {
    // Reset to default values
    setHasChanges(false);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: 'background.default',
        transition: 'all 0.3s ease-in-out',
        ...(isMaximized && {
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 9999,
          bgcolor: 'background.paper'
        })
      }}
    >
      <Container
        maxWidth={isMaximized ? false : 'xl'}
        sx={{
          py: 3,
          px: isMaximized ? 3 : undefined,
          height: isMaximized ? '100vh' : 'auto',
          overflow: isMaximized ? 'auto' : 'visible'
        }}
      >
        <PageTitle
          title="Admin Settings"
          description="Configure system settings and preferences"
          icon={<SettingsIcon />}
          breadcrumbs={[
            {
              label: 'Admin Dashboard',
              onClick: () => navigate('/app/admin-dashboard')
            },
            {
              label: 'Settings'
            }
          ]}
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Reset to Defaults">
                <IconButton
                  onClick={handleResetSettings}
                  sx={{
                    bgcolor: 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title={isMaximized ? 'Minimize' : 'Maximize'}>
                <IconButton
                  onClick={handleToggleMaximize}
                  sx={{
                    bgcolor: 'action.hover',
                    '&:hover': { bgcolor: 'action.selected' }
                  }}
                >
                  {isMaximized ? <FullscreenExit /> : <FullscreenIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          }
        />

        {/* Save Changes Alert */}
        {hasChanges && (
          <Alert
            severity="info"
            action={
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button color="inherit" size="small" onClick={handleResetSettings}>
                  Reset
                </Button>
                <Button color="inherit" size="small" onClick={handleSaveSettings}>
                  Save
                </Button>
              </Box>
            }
            sx={{ mb: 3 }}
          >
            You have unsaved changes. Don't forget to save your settings.
          </Alert>
        )}

        {/* Settings Tabs */}
        <Paper>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={activeTab} onChange={handleTabChange} aria-label="settings tabs">
              <Tab icon={<GeneralIcon />} label="General" />
              <Tab icon={<SecurityIcon />} label="Security" />
              <Tab icon={<NotificationsIcon />} label="Notifications" />
              <Tab icon={<StorageIcon />} label="Storage" />
              <Tab icon={<EmailIcon />} label="Email" />
              <Tab icon={<BackupIcon />} label="Backup" />
            </Tabs>
          </Box>

          {/* General Settings */}
          <TabPanel value={activeTab} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Site Name"
                  value={settings.general.siteName}
                  onChange={(e) => handleSettingChange('general', 'siteName', e.target.value)}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="Site Description"
                  multiline
                  rows={3}
                  value={settings.general.siteDescription}
                  onChange={(e) => handleSettingChange('general', 'siteDescription', e.target.value)}
                  sx={{ mb: 2 }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      System Options
                    </Typography>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.general.maintenanceMode}
                          onChange={(e) => handleSettingChange('general', 'maintenanceMode', e.target.checked)}
                        />
                      }
                      label="Maintenance Mode"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.general.registrationEnabled}
                          onChange={(e) => handleSettingChange('general', 'registrationEnabled', e.target.checked)}
                        />
                      }
                      label="User Registration Enabled"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.general.emailVerificationRequired}
                          onChange={(e) => handleSettingChange('general', 'emailVerificationRequired', e.target.checked)}
                        />
                      }
                      label="Email Verification Required"
                    />
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Security Settings */}
          <TabPanel value={activeTab} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  type="number"
                  label="Minimum Password Length"
                  value={settings.security.passwordMinLength}
                  onChange={(e) => handleSettingChange('security', 'passwordMinLength', parseInt(e.target.value))}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  type="number"
                  label="Session Timeout (minutes)"
                  value={settings.security.sessionTimeout}
                  onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  type="number"
                  label="Max Login Attempts"
                  value={settings.security.maxLoginAttempts}
                  onChange={(e) => handleSettingChange('security', 'maxLoginAttempts', parseInt(e.target.value))}
                  sx={{ mb: 2 }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Security Options
                    </Typography>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.security.requireSpecialChars}
                          onChange={(e) => handleSettingChange('security', 'requireSpecialChars', e.target.checked)}
                        />
                      }
                      label="Require Special Characters in Passwords"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.security.twoFactorEnabled}
                          onChange={(e) => handleSettingChange('security', 'twoFactorEnabled', e.target.checked)}
                        />
                      }
                      label="Enable Two-Factor Authentication"
                    />
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Notification Settings */}
          <TabPanel value={activeTab} index={2}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Notification Preferences
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.notifications.emailNotifications}
                          onChange={(e) => handleSettingChange('notifications', 'emailNotifications', e.target.checked)}
                        />
                      }
                      label="Email Notifications"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.notifications.systemAlerts}
                          onChange={(e) => handleSettingChange('notifications', 'systemAlerts', e.target.checked)}
                        />
                      }
                      label="System Alerts"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.notifications.userRegistrations}
                          onChange={(e) => handleSettingChange('notifications', 'userRegistrations', e.target.checked)}
                        />
                      }
                      label="User Registration Notifications"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.notifications.quotaWarnings}
                          onChange={(e) => handleSettingChange('notifications', 'quotaWarnings', e.target.checked)}
                        />
                      }
                      label="Quota Warning Notifications"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.notifications.backupReports}
                          onChange={(e) => handleSettingChange('notifications', 'backupReports', e.target.checked)}
                        />
                      }
                      label="Backup Report Notifications"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </TabPanel>

          {/* Storage Settings */}
          <TabPanel value={activeTab} index={3}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  type="number"
                  label="Max File Size (MB)"
                  value={settings.storage.maxFileSize}
                  onChange={(e) => handleSettingChange('storage', 'maxFileSize', parseInt(e.target.value))}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  type="number"
                  label="Auto Cleanup Days"
                  value={settings.storage.cleanupDays}
                  onChange={(e) => handleSettingChange('storage', 'cleanupDays', parseInt(e.target.value))}
                  sx={{ mb: 2 }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Storage Options
                    </Typography>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.storage.autoCleanup}
                          onChange={(e) => handleSettingChange('storage', 'autoCleanup', e.target.checked)}
                        />
                      }
                      label="Enable Auto Cleanup"
                    />
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Allowed File Types
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {settings.storage.allowedFileTypes.map((type) => (
                          <Chip key={type} label={type} size="small" />
                        ))}
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Email Settings */}
          <TabPanel value={activeTab} index={4}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="SMTP Host"
                  value={settings.email.smtpHost}
                  onChange={(e) => handleSettingChange('email', 'smtpHost', e.target.value)}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  type="number"
                  label="SMTP Port"
                  value={settings.email.smtpPort}
                  onChange={(e) => handleSettingChange('email', 'smtpPort', parseInt(e.target.value))}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="SMTP Username"
                  value={settings.email.smtpUser}
                  onChange={(e) => handleSettingChange('email', 'smtpUser', e.target.value)}
                  sx={{ mb: 2 }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  type="password"
                  label="SMTP Password"
                  value={settings.email.smtpPassword}
                  onChange={(e) => handleSettingChange('email', 'smtpPassword', e.target.value)}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="From Email"
                  value={settings.email.fromEmail}
                  onChange={(e) => handleSettingChange('email', 'fromEmail', e.target.value)}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="From Name"
                  value={settings.email.fromName}
                  onChange={(e) => handleSettingChange('email', 'fromName', e.target.value)}
                  sx={{ mb: 2 }}
                />
              </Grid>
            </Grid>
          </TabPanel>

          {/* Backup Settings */}
          <TabPanel value={activeTab} index={5}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth sx={{ mb: 2 }}>
                  <InputLabel>Backup Frequency</InputLabel>
                  <Select
                    value={settings.backup.backupFrequency}
                    label="Backup Frequency"
                    onChange={(e) => handleSettingChange('backup', 'backupFrequency', e.target.value)}
                  >
                    <MenuItem value="hourly">Hourly</MenuItem>
                    <MenuItem value="daily">Daily</MenuItem>
                    <MenuItem value="weekly">Weekly</MenuItem>
                    <MenuItem value="monthly">Monthly</MenuItem>
                  </Select>
                </FormControl>
                <TextField
                  fullWidth
                  type="number"
                  label="Retention Days"
                  value={settings.backup.retentionDays}
                  onChange={(e) => handleSettingChange('backup', 'retentionDays', parseInt(e.target.value))}
                  sx={{ mb: 2 }}
                />
                <TextField
                  fullWidth
                  label="Backup Location"
                  value={settings.backup.backupLocation}
                  onChange={(e) => handleSettingChange('backup', 'backupLocation', e.target.value)}
                  sx={{ mb: 2 }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Backup Options
                    </Typography>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.backup.autoBackup}
                          onChange={(e) => handleSettingChange('backup', 'autoBackup', e.target.checked)}
                        />
                      }
                      label="Enable Automatic Backups"
                    />
                    <Box sx={{ mt: 2 }}>
                      <Button
                        variant="outlined"
                        startIcon={<BackupIcon />}
                        sx={{ mr: 1, mb: 1 }}
                      >
                        Run Backup Now
                      </Button>
                      <Button
                        variant="outlined"
                        startIcon={<UpdateIcon />}
                        sx={{ mb: 1 }}
                      >
                        Test Connection
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>
        </Paper>

        {/* Save Button */}
        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button
            variant="outlined"
            onClick={handleResetSettings}
            disabled={!hasChanges}
          >
            Reset Changes
          </Button>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSaveSettings}
            disabled={!hasChanges}
          >
            Save Settings
          </Button>
        </Box>
      </Container>
    </Box>
  );
};

export default AdminSettingsPage;